/**
 * this handler to list and item of payment methods and update payment method data
 */
import { apiEndpoints, apiMethods, httpCode } from '@/src/data';
import { BackendClient } from '@/src/lib';
import {
  paymentMethodsApiResponseSchema,
  returnPaymentMethodsParams,
} from '@/src/store/wallet/payment-methods';
import { updatePaymentMethodBackendRequestSchema } from '@/src/store/wallet/payment-methods/request-transformer';
import { paymentMethodApiResponseSchema } from '@/src/store/wallet/payment-methods/response-transformer';
import { getJwt } from '@/src/utils';
import createApiError from '@/src/utils/api-handlers/create-api-error';
import createApiResponse from '@/src/utils/api-handlers/create-api-response';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id: itemId } = req.query;
  // this function to get payment method data by pass his id was getting from request query to this function url.
  // the function will return payment method data.
  if (itemId && itemId !== 'undefined' && req.method === apiMethods.GET) {
    try {
      const { data } = await BackendClient(req).get(
        apiEndpoints.paymentMethods(`${itemId}`),
        {
          headers: {
            authorization: token,
          },
          params: {
            'populate[icon][populate]': '*',
            'populate[deposit_custom_fields][populate]': '*',
            'populate[withdraw_custom_fields][populate]': '*',
            'populate[deposit_currencies][populate]': '*',
            'populate[withdraw_currencies][populate]': '*',
            'populate[badges][populate]': '*',
            'populate[created_by_user][populate]': '*',
            'populate[updated_by_user][populate]': '*',
          },
        },
      );
      return res
        .status(httpCode.SUCCESS)
        .json(paymentMethodApiResponseSchema(data?.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function return list of payment methods data after filter it by filters params and pagination
  } else if (req.method === 'GET') {
    try {
      const { data } = await BackendClient(req).get(
        apiEndpoints.paymentMethods(),
        {
          headers: {
            authorization: token,
          },
          params: { ...returnPaymentMethodsParams(req) },
        },
      );
      return createApiResponse(res, paymentMethodsApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function to update payment method data by get payment method id from request and pass it in function url
    // to update payment method data for this id
    // this function return updated payment method data
  } else if (
    itemId
    && itemId !== 'undefined'
    && req.method === apiMethods.PUT
  ) {
    try {
      const { data } = await BackendClient(req).put(
        apiEndpoints.paymentMethods(`${itemId}`),
        updatePaymentMethodBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
        },
      );

      return res
        .status(httpCode.SUCCESS)
        .json(paymentMethodApiResponseSchema(data?.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}
export default handler;
