import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import axios from 'axios';
import { authOptions } from '../auth/[...nextauth]';
import { handleBackendError } from '../../../src/utils/handle-backend-error';

const API_BASE_URL = process.env.BACKEND_API_URL || 'http://localhost:8000';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session || !session.user) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    if (!session.accessToken) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
      });
    }

    const response = await axios.get(`${API_BASE_URL}/api/notifications/unread-count`, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    return res.status(200).json(response.data);
  } catch (error: any) {
    // eslint-disable-next-line no-console
    console.error('Unread count API error:', error);

    // Use the proper error handling utility
    const errorKey = handleBackendError(error, []);

    // Return structured error response
    const statusCode = error.response?.status || 500;
    const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || 'Failed to fetch unread count';

    return res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: errorKey,
    });
  }
}
