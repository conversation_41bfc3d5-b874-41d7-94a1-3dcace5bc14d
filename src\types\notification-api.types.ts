/* eslint-disable max-lines */
// API Request/Response Types for Notification Management

import {
  AdminNotification,
  BroadcastMessageData,
  BroadcastPreview,
  NotificationTemplate,
  NotificationTemplateData,
  NotificationAnalytics,
  NotificationFilters,
  AnalyticsFilters,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  DeliveryStatus,
  TemplateVariable,
  PaginationState,
  AdminNotificationType,
} from './notification.types';
import { ApiResponse, PaginatedResponse } from './admin.types';

// Admin Notifications API Types
export interface GetAdminNotificationsRequest {
  page?: number;
  limit?: number;
  search?: string;
  status?: NotificationStatus;
  type?: NotificationType;
  priority?: NotificationPriority;
  createdBy?: string;
  fromDate?: string;
  toDate?: string;
  deliveryStatus?: DeliveryStatus;
}

export interface GetAdminNotificationsResponse extends PaginatedResponse<AdminNotification, 'notifications'> {}

export interface GetNotificationDetailsRequest {
  id: string;
}

export interface GetNotificationDetailsResponse extends ApiResponse<AdminNotification> {}

export interface UpdateNotificationStatusRequest {
  id: string;
  status: NotificationStatus;
  reason?: string;
}

export interface UpdateNotificationStatusResponse extends ApiResponse<AdminNotification> {}

export interface BulkNotificationActionRequest {
  action: 'mark_read' | 'delete' | 'resend';
  notificationIds: string[];
  reason?: string;
}

export interface BulkNotificationActionResponse extends ApiResponse<{
  successful: string[];
  failed: Array<{
    id: string;
    error: string;
  }>;
}> {}

export interface ResendNotificationRequest {
  id: string;
  recipientIds?: string[];
}

export interface ResendNotificationResponse extends ApiResponse<{
  notificationId: string;
  recipientCount: number;
  estimatedDeliveryTime: number;
}> {}

// Broadcast Message API Types
export interface SendBroadcastRequest {
  title: string;
  content: string;
  priority: NotificationPriority;
  recipients: {
    type: 'all' | 'role' | 'individual';
    roles?: string[];
    userIds?: string[];
  };
  scheduleTime?: string;
  templateId?: string;
  variables?: Record<string, string>;
}

export interface SendBroadcastResponse extends ApiResponse<{
  notificationId: string;
  recipientCount: number;
  scheduledAt?: string;
  estimatedDeliveryTime: number;
}> {}

export interface ScheduleBroadcastRequest extends SendBroadcastRequest {
  scheduleTime: string;
}

export interface ScheduleBroadcastResponse extends SendBroadcastResponse {}

export interface PreviewBroadcastRequest {
  title: string;
  content: string;
  recipients: {
    type: 'all' | 'role' | 'individual';
    roles?: string[];
    userIds?: string[];
  };
  templateId?: string;
  variables?: Record<string, string>;
}

export interface PreviewBroadcastResponse extends ApiResponse<BroadcastPreview> {}

export interface GetRecipientOptionsRequest {
  search?: string;
  type?: 'user' | 'role';
}

export interface GetRecipientOptionsResponse extends ApiResponse<{
  users: Array<{
    id: string;
    name: string;
    email: string;
    type: string;
  }>;
  roles: Array<{
    id: string;
    name: string;
    userCount: number;
  }>;
}> {}

// Template Management API Types
export interface GetTemplatesRequest {
  page?: number;
  limit?: number;
  search?: string;
  type?: NotificationType;
  isActive?: boolean;
  createdBy?: string;
}

export interface GetTemplatesResponse extends PaginatedResponse<NotificationTemplate, 'templates'> {}

export interface GetTemplateRequest {
  id: string;
}

export interface GetTemplateResponse extends ApiResponse<NotificationTemplate> {}

export interface CreateTemplateRequest {
  name: string;
  description?: string;
  subject: string;
  content: string;
  type: NotificationType;
  variables: TemplateVariable[];
  isActive?: boolean;
}

export interface CreateTemplateResponse extends ApiResponse<NotificationTemplate> {}

export interface UpdateTemplateRequest {
  id: string;
  name?: string;
  description?: string;
  subject?: string;
  content?: string;
  type?: NotificationType;
  variables?: TemplateVariable[];
  isActive?: boolean;
}

export interface UpdateTemplateResponse extends ApiResponse<NotificationTemplate> {}

export interface DeleteTemplateRequest {
  id: string;
}

export interface DeleteTemplateResponse extends ApiResponse<{
  deleted: boolean;
  templateId: string;
}> {}

export interface DuplicateTemplateRequest {
  id: string;
  newName: string;
}

export interface DuplicateTemplateResponse extends ApiResponse<NotificationTemplate> {}

export interface GetTemplateUsageRequest {
  id: string;
  fromDate?: string;
  toDate?: string;
}

export interface GetTemplateUsageResponse extends ApiResponse<{
  templateId: string;
  usageCount: number;
  recentUsage: Array<{
    date: string;
    count: number;
    notificationIds: string[];
  }>;
}> {}

export interface PreviewTemplateRequest {
  id: string;
  variables?: Record<string, string>;
}

export interface PreviewTemplateResponse extends ApiResponse<{
  renderedSubject: string;
  renderedContent: string;
  missingVariables: string[];
}> {}

// Analytics API Types
export interface GetNotificationAnalyticsRequest {
  fromDate: string;
  toDate: string;
  notificationType?: NotificationType;
  recipientType?: 'all' | 'role' | 'individual';
  priority?: NotificationPriority;
  groupBy?: 'day' | 'week' | 'month';
}

export interface GetNotificationAnalyticsResponse extends ApiResponse<NotificationAnalytics> {}

export interface GetDeliveryAnalyticsRequest {
  fromDate: string;
  toDate: string;
  notificationIds?: string[];
}

export interface GetDeliveryAnalyticsResponse extends ApiResponse<{
  totalNotifications: number;
  deliveryStats: {
    delivered: number;
    failed: number;
    pending: number;
  };
  failureReasons: Array<{
    reason: string;
    count: number;
    percentage: number;
  }>;
  deliveryTrends: Array<{
    date: string;
    delivered: number;
    failed: number;
  }>;
}> {}

export interface GetEngagementAnalyticsRequest {
  fromDate: string;
  toDate: string;
  notificationType?: NotificationType;
}

export interface GetEngagementAnalyticsResponse extends ApiResponse<{
  totalSent: number;
  engagementStats: {
    opened: number;
    clicked: number;
    openRate: number;
    clickRate: number;
  };
  engagementTrends: Array<{
    date: string;
    opened: number;
    clicked: number;
  }>;
  topPerformingNotifications: Array<{
    id: string;
    title: string;
    openRate: number;
    clickRate: number;
  }>;
}> {}

export interface ExportAnalyticsRequest {
  fromDate: string;
  toDate: string;
  format: 'csv' | 'excel';
  includeDetails?: boolean;
  notificationType?: NotificationType;
}

export interface ExportAnalyticsResponse extends ApiResponse<{
  downloadUrl: string;
  fileName: string;
  expiresAt: string;
}> {}

// Real-time Updates API Types
export interface NotificationStatusUpdate {
  notificationId: string;
  status: NotificationStatus;
  deliveryStatus: DeliveryStatus;
  deliveredCount: number;
  failedCount: number;
  timestamp: string;
}

export interface BulkOperationProgress {
  operationId: string;
  type: 'mark_read' | 'delete' | 'resend';
  total: number;
  completed: number;
  failed: number;
  status: 'in_progress' | 'completed' | 'failed';
  errors?: Array<{
    notificationId: string;
    error: string;
  }>;
}

// System Settings API Types
export interface GetNotificationSettingsRequest {}

export interface GetNotificationSettingsResponse extends ApiResponse<{
  deliverySettings: {
    maxConcurrentDeliveries: number;
    retryAttempts: number;
    retryDelay: number;
  };
  rateLimiting: {
    dailyLimit: number;
    hourlyLimit: number;
    currentUsage: {
      daily: number;
      hourly: number;
    };
  };
  enabledNotificationTypes: NotificationType[];
  defaultPriority: NotificationPriority;
}> {}

export interface UpdateNotificationSettingsRequest {
  deliverySettings?: {
    maxConcurrentDeliveries?: number;
    retryAttempts?: number;
    retryDelay?: number;
  };
  rateLimiting?: {
    dailyLimit?: number;
    hourlyLimit?: number;
  };
  enabledNotificationTypes?: NotificationType[];
  defaultPriority?: NotificationPriority;
}

export interface UpdateNotificationSettingsResponse extends ApiResponse<{
  updated: boolean;
  settings: unknown;
}> {}

export interface TestNotificationRequest {
  recipientEmail: string;
  title: string;
  content: string;
  type: NotificationType;
}

export interface TestNotificationResponse extends ApiResponse<{
  sent: boolean;
  deliveryTime: number;
  testId: string;
}> {}
