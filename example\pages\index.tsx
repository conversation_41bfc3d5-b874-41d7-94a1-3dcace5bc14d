/**
 * This component renders payment methods page.
 * Display table to show list of payment methods with pagination and filter it by some parameters and search at the payment methods.
 */
import Layout from '@/src/components/layout';
import {
  GetPaymentMethodsColumns,
  PaymentMethodsColumnsName,
  PaymentMethodsHeaders,
} from '@/src/components/table/table-columns';
import { CurrenciesApiResponse } from '@/src/store/wallet/currencies';
import { withDate } from '@/src/utils';
import {
  Grid, Group, MultiSelect, Title,
} from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { DataTableSortStatus } from 'mantine-datatable';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { DateTimePicker } from '@mantine/dates';
import Table from '@/src/components/table/table';
import {
  getPaymentMethodsQuery,
  paymentMethodTag,
  sortKeysMapping,
} from '@/src/store/wallet/payment-methods';
import { PageProps } from '@/src/types/page-prop.type';
import { useSearch } from '@/src/hooks/useSearch';
import { BadgesApiResponse, getBadgesQuery } from '@/src/store/wallet/badges';

export default function PaymentMethods({ authenticated }: PageProps) {
  const router = useRouter();
  const { searchValue } = useSearch();
  // get filters value from page route.
  const createdAtFilterGte = `${router.query.createdAtGte}`;
  const createdAtFilterLte = `${router.query.createdAtLte}`;
  const tagsFilterLte = `${router.query.tags}`;
  // this state to handle csv data fetching enabled
  const [csvFetchData, setCsvFetchData] = useState(false);
  // filters and pagination states.
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [createdAtGte, setCreateAtGte] = useState<string | null>(null);
  const [createdAtLte, setCreateAtLte] = useState<string | null>(null);
  const [sortStatus, setSortStatus] = useState<DataTableSortStatus>({
    columnAccessor: 'id',
    direction: 'desc',
  });
  const [badges, setBadges] = useState<Array<{id: string, label: string}>>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const { data: badgeData, isLoading: isLoadingBadgeData } = useQuery(
    getBadgesQuery({
      pagination: {
        pageSize: 100000,
      },
    }),
  );
  const badgesFilter = router.query.badges
    ? (Array.isArray(router.query.badges)
      ? router.query.badges
      : [router.query.badges]
    ).map((label) => {
      const badge = badgeData?.data.find(
        (b: BadgesApiResponse['data'][0]) => b.label === label,
      );
      return badge
        ? { id: badge.id.toString(), label: badge.label }
        : null;
    }).filter((badge): badge is {id: string, label: string} => badge !== null)
    : [];

  // This effect to check if there are a filters in page route with undefined values
  //  and  delete are from page route.
  // else set the value in his filter state.
  useEffect(() => {
    setCreateAtGte(
      createdAtFilterGte !== 'undefined' ? createdAtFilterGte : null,
    );
    setCreateAtLte(
      createdAtFilterLte !== 'undefined' ? createdAtFilterLte : null,
    );
    // setSelectedTags(
    //   tagsFilterLte !== 'undefined' ? tagsFilterLte : null,
    // );

    if (router.query.badges) {
      const urlBadges = Array.isArray(router.query.badges)
        ? router.query.badges
        : [router.query.badges];

      const matchedBadges = urlBadges
        .map((label) => {
          const badge = badgeData?.data.find(
            (b: BadgesApiResponse['data'][0]) => b.label === label,
          );
          return badge
            ? { id: badge.id.toString(), label: badge.label }
            : null;
        })
        .filter((badge): badge is {id: string, label: string} => badge !== null);

      setBadges(matchedBadges);
    } else {
      setBadges([]);
    }

    if (!tagsFilterLte) {
      delete router.query.tagsFilterLte;
      router.push(router);
    }
    if (!createdAtFilterGte) {
      delete router.query.createdAtGte;
      router.push(router);
    }
    if (!createdAtFilterLte) {
      delete router.query.createdAtLte;
      router.push(router);
    }
    if (!badgesFilter) {
      delete router.query.badges;
      router.push(router);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router?.query, badgeData?.data]);
  // filters params to set it in data fetching function.
  const filters = {
    search: searchValue,
    createdAtGte,
    createdAtLte,
    badges: badges.map((badge) => badge.id),
    tags: selectedTags,
  };
  const sortKey = `${
    sortKeysMapping.has(sortStatus.columnAccessor)
      ? sortKeysMapping.get(sortStatus.columnAccessor)
      : sortStatus.columnAccessor
  }:${sortStatus.direction}`;
  // Data fetching function
  // return list of payment methods with pagination
  const { data, isLoading } = useQuery(
    getPaymentMethodsQuery({
      pagination: {
        page,
        pageSize,
      },
      populate: {
        depositCurrencies: true,
        withdrawCurrencies: true,
      },
      filters,
      sort: sortKey,
    }),
  );
  const tags = Object.values(paymentMethodTag.Values).map((tag) => ({
    value: tag,
    label: tag,
  }));
  // Data fetching for export it as csv file
  // The page size is very large becaus we need ti get all payment methods.
  const dataCsv = useQuery({
    ...getPaymentMethodsQuery({
      pagination: {
        pageSize: 100000,
      },
      filters,
      sort: sortKey,
    }),
    enabled: csvFetchData,
    onSuccess: () => setCsvFetchData(false),
  });
  // function to handle filters changing and add the their values to page route
  function handleChangeValue({
    filterBy,
    value,
  }: {
    filterBy: 'createdAtLte' | 'createdAtGte';
    value: string;
  }) {
    setPage(1);
    if (filterBy === 'createdAtGte') {
      router.query.createdAtGte = value;
    }
    if (filterBy === 'createdAtLte') {
      router.query.createdAtLte = value;
    }
    router.push(router);
  }

  const handleBadgesChange = (selectedIds: string[]) => {
    setPage(1);
    const selectedBadges = selectedIds.map((id) => {
      const badge = badgeData?.data.find(
        (b: BadgesApiResponse['data'][0]) => b.id.toString() === id,
      );
      return badge
        ? { id, label: badge.label }
        : null;
    }).filter((badge): badge is {id: string, label: string} => badge !== null);
    setBadges(selectedBadges);
    if (selectedBadges.length > 0) {
      router.query.badges = selectedBadges.map((badge) => badge.label);
    } else {
      delete router.query.badges;
    }
    router.push({
      pathname: router.pathname,
      query: router.query,
    });
  };

  const handleTagsChange = (selected: string[]) => {
    setPage(1);
    setSelectedTags(selected);
    if (selected.length > 0) {
      router.query.tags = selected;
    } else {
      delete router.query.tags;
    }
    router.push({
      pathname: router.pathname,
      query: router.query,
    });
  };

  return (
    <div>
      <Head>
        <title>Payment Methods | Admin</title>
      </Head>
      <Layout>
        <Grid>
          <Grid.Col span={12}>
            <Title m="xs" order={4}>
              Payment Methods
            </Title>
            <Table
              fileName="currencies"
              // here used withDate function to format the date to be readd for user
              csvData={dataCsv?.data?.data?.map(
                (i: CurrenciesApiResponse['data'][0]) => withDate({ items: ['createdAt'], object: i }),
              )}
              headers={PaymentMethodsHeaders}
              data={data?.data}
              columns={GetPaymentMethodsColumns()}
              columnsName={PaymentMethodsColumnsName}
              fetching={isLoading}
              page={page}
              setPage={setPage}
              pageSize={pageSize}
              setPageSize={setPageSize}
              total={data?.pagination?.total}
              sortStatus={sortStatus}
              onSortStatusChange={setSortStatus}
              searchPlaceholder="Search ID,Title"
              isCsvDataLoading={dataCsv.isFetching}
              setCsvFetchData={setCsvFetchData}
              additionalSearchInput={undefined}
            >
              <Group gap="xs">
                <MultiSelect
                  searchable
                  data={tags}
                  value={selectedTags}
                  onChange={handleTagsChange}
                  placeholder="Select Tags"
                  clearable
                  disabled={isLoading}
                />
                <MultiSelect
                  searchable
                  data={
                    badgeData?.data.map((badge: BadgesApiResponse['data'][0]) => ({
                      value: badge.id.toString(),
                      label: badge.label,
                    })) || []
                  }
                  value={badges.map((badge) => badge.id)}
                  onChange={handleBadgesChange}
                  placeholder="Select Badges"
                  clearable
                  disabled={isLoading || isLoadingBadgeData}
                />
                <DateTimePicker
                  miw={120}
                  clearable
                  disabled={isLoading}
                  placeholder="Create At (GTE)"
                  mx="auto"
                  onChange={(v) => handleChangeValue({
                    filterBy: 'createdAtGte',
                    value: v ? `${v.toISOString()}` : '',
                  })}
                  value={createdAtGte ? new Date(createdAtGte) : null}
                />
                <DateTimePicker
                  miw={120}
                  clearable
                  disabled={isLoading}
                  placeholder="Create At (LTE)"
                  mx="auto"
                  onChange={(v) => handleChangeValue({
                    filterBy: 'createdAtLte',
                    value: v ? `${v.toISOString()}` : '',
                  })}
                  value={createdAtLte ? new Date(createdAtLte) : null}
                />
              </Group>
            </Table>
          </Grid.Col>
        </Grid>
      </Layout>
    </div>
  );
}
