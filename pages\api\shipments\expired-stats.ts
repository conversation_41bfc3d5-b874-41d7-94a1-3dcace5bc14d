import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data/api-endpoints';
import { getJwt } from '../../../src/utils/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Get the JWT token from the request
    const { token } = await getJwt(req);

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'No token provided',
        },
      });
    }

    // Make the request to the backend with the token
    const response = await BACKEND_API.get(API_ENDPOINT.shipments.expiredStats, {
      headers: {
        Authorization: token,
      },
    });

    // Return the response data
    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error('Expired shipment stats API error:', error);

    // Handle different types of errors
    if (error.response) {
      // Backend returned an error response
      return res.status(error.response.status).json(error.response.data);
    } else if (error.request) {
      // Network error
      return res.status(500).json({
        success: false,
        error: {
          type: 'NETWORK_ERROR',
          message: 'Failed to connect to backend',
        },
      });
    } else {
      // Other error
      return res.status(500).json({
        success: false,
        error: {
          type: 'INTERNAL_ERROR',
          message: 'Internal server error',
        },
      });
    }
  }
}
