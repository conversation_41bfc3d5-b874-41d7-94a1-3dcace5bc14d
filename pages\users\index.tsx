/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable max-lines */
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Group,
  TextInput,
  Select,
  Button,
  Table,
  Badge,
  ActionIcon,
  Pagination,

  Alert,
  Text,
  Stack,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconEye,
  IconRefresh,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useLoading } from '../../src/contexts/LoadingContext';
import {
  getUserListQuery,
  UserListParamsType,
} from '../../src/requests/admin-users';
import {
  UserType, UserStatus, ApprovalStatus, User,
} from '../../src/types/admin.types';

const USER_TYPE_OPTIONS = [
  { value: '', label: 'All User Types' },
  { value: UserType.CUSTOMER, label: 'Customer' },
  { value: UserType.ACCESS_OPERATOR, label: 'Access Operator' },
  { value: UserType.CAR_OPERATOR, label: 'Car Operator' },
];

const STATUS_OPTIONS = [
  { value: '', label: 'All Statuses' },
  { value: UserStatus.ACTIVE, label: 'Active' },
  { value: UserStatus.PENDING, label: 'Pending' },
  { value: UserStatus.SUSPENDED, label: 'Suspended' },
];

const APPROVAL_STATUS_OPTIONS = [
  { value: '', label: 'All Approval Statuses' },
  { value: ApprovalStatus.APPROVED, label: 'Approved' },
  { value: ApprovalStatus.PENDING, label: 'Pending' },
];

export default function UsersPage() {
  const router = useRouter();
  const { status: sessionStatus } = useSession();
  const { showLoading, hideLoading } = useLoading();

  // Filter state
  const [filters, setFilters] = useState<UserListParamsType>({
    page: 1,
    limit: 20,
    search: '',
    user_type: undefined,
    status: undefined,
    approval_status: undefined,
  });

  // Queries and mutations
  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    ...getUserListQuery(filters),
  });

  // Use global loading for this query
  useEffect(() => {
    if (isLoading) {
      showLoading('Loading users...');
    } else {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading]);
  // Handle authentication
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/login');
    }
  }, [sessionStatus, router]);

  // Event handlers
  const handleFilterChange = (key: keyof UserListParamsType, value: string | null) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value || undefined,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleViewUser = (user: User) => {
    router.push(`/users/detail?id=${user.id}`);
  };

  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      search: '',
      user_type: undefined,
      status: undefined,
      approval_status: undefined,
    });
  };

  // Helper functions
  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE:
        return 'green';
      case UserStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  const getApprovalStatusColor = (status: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.APPROVED:
        return 'green';
      case ApprovalStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading users"
          color="red"
          variant="light"
        >
          <Text mb="md">
            Failed to load user data. Please try again.
          </Text>
          <Button variant="outline" onClick={() => refetch()}>
            Retry
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="lg">
        <Title order={1}>User Management</Title>
        <Button
          leftSection={<IconRefresh size="1rem" />}
          variant="outline"
          onClick={() => refetch()}
          loading={false}
        >
          Refresh
        </Button>
      </Group>

      {/* Filters */}
      <Paper withBorder p="md" mb="lg">
        <Stack gap="md">
          <Group>
            <TextInput
              placeholder="Search users..."
              leftSection={<IconSearch size="1rem" />}
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              style={{ flex: 1 }}
            />
            <Button
              variant="outline"
              leftSection={<IconFilter size="1rem" />}
              onClick={clearFilters}
            >
              Clear Filters
            </Button>
          </Group>

          <Group>
            <Select
              placeholder="User Type"
              data={USER_TYPE_OPTIONS}
              value={filters.user_type || ''}
              onChange={(value) => handleFilterChange('user_type', value)}
              clearable
            />
            <Select
              placeholder="Status"
              data={STATUS_OPTIONS}
              value={filters.status || ''}
              onChange={(value) => handleFilterChange('status', value)}
              clearable
            />
            <Select
              placeholder="Approval Status"
              data={APPROVAL_STATUS_OPTIONS}
              value={filters.approval_status || ''}
              onChange={(value) => handleFilterChange('approval_status', value)}
              clearable
            />
          </Group>
        </Stack>
      </Paper>

      {/* Users Table */}
      <Paper withBorder>
        {usersData?.data?.users?.length === 0 ? (
          <Text ta="center" py="xl" c="dimmed">
            No users found matching your criteria
          </Text>
        ) : (
          <>
            <Table.ScrollContainer minWidth={800}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Name</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Approval</Table.Th>
                    <Table.Th>Created</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {usersData?.data?.users?.map((user) => (
                    <Table.Tr key={user.id}>
                      <Table.Td>
                        <div>
                          <Text fw={500}>{user.name}</Text>
                          {user.phone && <Text size="xs" c="dimmed">{user.phone}</Text>}
                        </div>
                      </Table.Td>
                      <Table.Td>{user.email}</Table.Td>
                      <Table.Td>
                        <Badge variant="light" color="blue">
                          {user.user_type.replace('_', ' ')}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(user.status as UserStatus)}>
                          {user.status}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Stack gap="xs">
                          {/* General Approval Status */}
                          <Badge color={getApprovalStatusColor((user.approval_status as ApprovalStatus) || ApprovalStatus.APPROVED)}>
                            {user.approval_status || ApprovalStatus.APPROVED}
                          </Badge>
                        </Stack>
                      </Table.Td>
                      <Table.Td>{formatDate(user.created_at)}</Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            color="blue"
                            onClick={() => handleViewUser(user as User)}
                            title="View Full Details"
                          >
                            <IconEye size="1rem" />
                          </ActionIcon>

                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Table.ScrollContainer>

            {/* Pagination */}
            {usersData?.data?.pagination && usersData?.data?.pagination?.totalPages > 1 && (
              <Group justify="center" p="md">
                <Pagination
                  value={usersData?.data?.pagination?.page}
                  onChange={handlePageChange}
                  total={usersData?.data?.pagination?.totalPages}
                />
              </Group>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
}
