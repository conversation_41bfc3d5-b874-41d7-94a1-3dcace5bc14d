/* eslint-disable @typescript-eslint/no-explicit-any */
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-api-error';
import {
  transformDashboardResponse,
  transformExpiredShipmentStatsResponse,
} from './response-transformer';
import {
  DashboardResponseType,
  ExpiredShipmentStatsResponseType,
} from './types';

// Type for error objects with status property
interface ErrorWithStatus {
  status?: number;
}

// API Request Functions
export const getDashboardStatsRequest = async (): Promise<DashboardResponseType> => {
  try {
    const response = await CLIENT_API.get('/dashboard');
    return transformDashboardResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const getExpiredShipmentStatsRequest = async (): Promise<ExpiredShipmentStatsResponseType> => {
  try {
    const response = await CLIENT_API.get('/shipments/expired-stats');
    return transformExpiredShipmentStatsResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

// React Query Configuration Objects
export const getDashboardStatsQuery = {
  queryKey: ['admin-dashboard-stats'],
  queryFn: getDashboardStatsRequest,
  refetchOnWindowFocus: false,
  staleTime: 5 * 60 * 1000, // 5 minutes
  retry: (failureCount: number, error: ErrorWithStatus) => {
    // Don't retry on authentication errors
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
};

export const getExpiredShipmentStatsQuery = {
  queryKey: ['admin-expired-shipment-stats'],
  queryFn: getExpiredShipmentStatsRequest,
  refetchOnWindowFocus: false,
  staleTime: 10 * 60 * 1000, // 10 minutes
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
};
