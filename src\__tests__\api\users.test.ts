/**
 * Tests for the users API endpoints
 * Tests both /api/users (list) and /api/users/[id] (detail) routes
 */
import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import usersList<PERSON>andler from '../../../pages/api/users/index';
import userDetailHandler from '../../../pages/api/users/[id]';

// Mock the dependencies
jest.mock('../../../src/lib/axios');
jest.mock('../../../src/utils/auth');
jest.mock('../../../src/requests/admin-users');

const mockGetJwt = require('../../../src/utils/auth').getJwt;
const mockBACKEND_API = require('../../../src/lib/axios').BACKEND_API;
const mockTransformUserListResponse = require('../../../src/requests/admin-users').transformUserListResponse;
const mockTransformUserDetailResponse = require('../../../src/requests/admin-users').transformUserDetailResponse;
const mockReturnUserListParams = require('../../../src/requests/admin-users').returnUserListParams;

describe('/api/users API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/users (list)', () => {
    it('should return 401 when no token is provided', async () => {
      mockGetJwt.mockResolvedValue({ token: null });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
      });

      await usersListHandler(req, res);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'No token provided',
        },
      });
    });

    it('should return users list when authenticated', async () => {
      const mockToken = 'Bearer mock-token';
      const mockBackendResponse = {
        data: {
          success: true,
          data: {
            users: [
              { id: '1', name: 'User 1', email: '<EMAIL>' },
              { id: '2', name: 'User 2', email: '<EMAIL>' },
            ],
            pagination: { page: 0, limit: 20, total: 2 },
          },
        },
      };
      const mockTransformedResponse = {
        success: true,
        data: mockBackendResponse.data.data,
      };

      mockGetJwt.mockResolvedValue({ token: mockToken });
      mockReturnUserListParams.mockReturnValue({});
      mockBACKEND_API.get.mockResolvedValue(mockBackendResponse);
      mockTransformUserListResponse.mockReturnValue(mockTransformedResponse);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
      });

      await usersListHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(mockTransformedResponse);
      expect(mockBACKEND_API.get).toHaveBeenCalledWith('/users', {
        params: {},
        headers: { Authorization: mockToken },
      });
    });
  });

  describe('GET /api/users/[id] (detail)', () => {
    it('should return 401 when no token is provided', async () => {
      mockGetJwt.mockResolvedValue({ token: null });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
        query: { id: 'eb4f5c1f-d8e7-478d-a6f8-6b79693e14d2' },
      });

      await userDetailHandler(req, res);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'No token provided',
        },
      });
    });

    it('should return 400 when no user ID is provided', async () => {
      const mockToken = 'Bearer mock-token';
      mockGetJwt.mockResolvedValue({ token: mockToken });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
        query: {},
      });

      await userDetailHandler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        error: {
          message: 'User ID is required',
          code: 400,
        },
      });
    });

    it('should return user details when authenticated and valid ID provided', async () => {
      const mockToken = 'Bearer mock-token';
      const userId = 'eb4f5c1f-d8e7-478d-a6f8-6b79693e14d2';
      const mockBackendResponse = {
        data: {
          success: true,
          data: {
            user: {
              id: userId,
              name: 'Test User',
              email: '<EMAIL>',
              status: 'active',
            },
          },
        },
      };
      const mockTransformedResponse = {
        success: true,
        data: mockBackendResponse.data.data,
      };

      mockGetJwt.mockResolvedValue({ token: mockToken });
      mockBACKEND_API.get.mockResolvedValue(mockBackendResponse);
      mockTransformUserDetailResponse.mockReturnValue(mockTransformedResponse);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
        query: { id: userId },
      });

      await userDetailHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(mockTransformedResponse);
      expect(mockBACKEND_API.get).toHaveBeenCalledWith('/users', {
        params: {
          id: userId,
          detail: 'true',
          _t: expect.any(Number),
        },
        headers: {
          Authorization: mockToken,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      });
    });

    it('should handle backend errors properly', async () => {
      const mockToken = 'Bearer mock-token';
      const userId = 'eb4f5c1f-d8e7-478d-a6f8-6b79693e14d2';
      const mockError = {
        response: {
          status: 404,
          data: { message: 'User not found' },
        },
      };

      mockGetJwt.mockResolvedValue({ token: mockToken });
      mockBACKEND_API.get.mockRejectedValue(mockError);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
        query: { id: userId },
      });

      await userDetailHandler(req, res);

      expect(res._getStatusCode()).toBe(404);
      const responseData = JSON.parse(res._getData());
      expect(responseData.success).toBe(false);
      expect(responseData.error.message).toBe('User not found');
    });
  });

  describe('Method not allowed', () => {
    it('should return 405 for unsupported methods on list endpoint', async () => {
      const mockToken = 'Bearer mock-token';
      mockGetJwt.mockResolvedValue({ token: mockToken });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'DELETE',
      });

      await usersListHandler(req, res);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        error: {
          message: 'Method not allowed',
          code: 405,
        },
      });
    });

    it('should return 405 for unsupported methods on detail endpoint', async () => {
      const mockToken = 'Bearer mock-token';
      mockGetJwt.mockResolvedValue({ token: mockToken });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'DELETE',
        query: { id: 'eb4f5c1f-d8e7-478d-a6f8-6b79693e14d2' },
      });

      await userDetailHandler(req, res);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        error: {
          message: 'Method not allowed',
          code: 405,
        },
      });
    });
  });
});
