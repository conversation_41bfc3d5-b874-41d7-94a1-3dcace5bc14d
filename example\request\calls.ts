import { ApiClient } from '@/src/lib';
import {
  UpdatePaymentMethodApiRequest,
  getPaymentMethodsQueryProps,
} from './types';
import { apiEndpoints, httpCode } from '@/src/data';
import { handleApiError } from '@/src/utils';
// react query keys
enum queryKeys {
  paymentMethods = 'payment-methods',
  paymentMethod = 'payment-method',
  update = 'update',
}
/**
 * @description This function calls to get all payment methods data.
 * There are some filter params to filter payment methods.
 * @param props
 * @returns list of payment methods with pagination.
 */
const getPaymentMethodsRequest = (props: getPaymentMethodsQueryProps) => {
  const {
    pagination, filters, populate, sort,
  } = props;
  return ApiClient.get(apiEndpoints.paymentMethods(), {
    params: {
      page: pagination?.page,
      pageSize: pagination?.pageSize,
      start: pagination?.start,
      limit: pagination?.limit,
      showInRatesBar: filters?.showInRatesBar,
      depositCurrencyId: filters?.depositCurrencyId,
      withdrawCurrencyId: filters?.withdrawCurrencyId,
      depositCurrencies: populate?.depositCurrencies,
      withdrawCurrencies: populate?.withdrawCurrencies,
      customFields: populate?.customFields,
      icon: populate?.icon,
      createdAtGte: filters?.createdAtGte,
      createdAtLte: filters?.createdAtLte,
      badges: filters?.badges,
      tags: filters?.tags,
      search: filters?.search,
      sort,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};
// get all payment methods query function
export const getPaymentMethodsQuery = (props: getPaymentMethodsQueryProps) => ({
  queryKey: [
    queryKeys.paymentMethods,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getPaymentMethodsRequest(props),
  refetchOnWindowFocus: false,
  retry: (failureCount: number, error: { code: httpCode }) => error?.code !== httpCode.UNAUTHORIZED,
});
/** ****************************************************************** */
/**
 * @description This function calls to get payment method data by id.
 * @param id
 * @returns payment method data.
 */
const getPaymentMethodRequest = (id: string) => ApiClient.get(apiEndpoints.paymentMethodsByIdAsParams(id))
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err.response.data;
  });
// get payment method data query function
export const getPaymentMethodQuery = (
  id: string,
  { isAuth }: { isAuth: boolean },
) => ({
  queryKey: [queryKeys.paymentMethod, id],
  queryFn: () => getPaymentMethodRequest(id),
  refetchOnWindowFocus: false,
  enabled: isAuth,
  retry: (failureCount: number, error: { code: httpCode }) => error?.code !== httpCode.UNAUTHORIZED,
});
/** **************************************************************** */
/**
 * @description This function calls to update payment method data by passing his id in request url.
 * @param 'id:payment method id , body:new payment method data'
 * @returns updated payment method data.
 */
const updatePaymentMethodRequest = ({
  body,
  id,
}: {
  id: string;
  body: UpdatePaymentMethodApiRequest;
}) => ApiClient.put(apiEndpoints.paymentMethodsByIdAsParams(id), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });
// update payment method data mutation function
export const updatePaymentMethodMutation = () => ({
  mutationKey: [queryKeys.update],
  mutationFn: ({
    body,
    id,
  }: {
    id: string;
    body: UpdatePaymentMethodApiRequest;
  }) => updatePaymentMethodRequest({ id, body }),
});
/** *********************************************************************** */
