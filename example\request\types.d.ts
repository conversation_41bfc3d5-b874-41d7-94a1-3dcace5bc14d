import { z } from 'zod';
import { paymentMethodsApiResponseSchema } from './response-transformer';
import { Pagination } from '@/src/types';
import { updatePaymentMethodApiRequestSchema } from './request-transformer';

export type Filter = {
  showInRatesBar?: boolean | null;
  depositCurrencyId?:string;
  withdrawCurrencyId?:string;
  search?: string;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  badges?: string[] | null;
  tags?: string[] | null;
};
export interface getPaymentMethodsQueryProps {
  populate?: {
    depositCurrencies?: boolean;
    withdrawCurrencies?: boolean;
    icon?:boolean
    customFields?:boolean
  };
  pagination?: Pagination;
  filters?:Filter;
  sort?: string;
}
export type PaymentMethodsApiResponse = z.infer<
  typeof paymentMethodsApiResponseSchema
>;

export type UpdatePaymentMethodApiRequest = z.infer<
  typeof updatePaymentMethodApiRequestSchema
>;
