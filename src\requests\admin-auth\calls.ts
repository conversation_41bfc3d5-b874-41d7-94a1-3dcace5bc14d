/* eslint-disable @typescript-eslint/no-explicit-any */
import { BACKEND_API } from '../../lib/axios';
import { API_ENDPOINT } from '../../data/api-endpoints';
import { handleApiError } from '../../utils/handle-api-error';
import {
  transformAdminLoginRequest,
  transformAdminRegisterRequest,
  transformOtpVerificationRequest,
  transformForgotPasswordRequest,
  transformResetPasswordRequest,
  transformChangePasswordRequest,
} from './request-transformer';
import {
  transformAdminLoginResponse,
  transformAdminRegisterResponse,
  transformOtpVerificationResponse,
  transformForgotPasswordResponse,
  transformResetPasswordResponse,
  transformChangePasswordResponse,
} from './response-transformer';
import {
  AdminLoginRequestType,
  AdminRegisterRequestType,
  OtpVerificationRequestType,
  ForgotPasswordRequestType,
  ResetPasswordRequestType,
  ChangePasswordRequestType,
  AdminLoginResponseType,
  AdminRegisterResponseType,
  OtpVerificationResponseType,
  ForgotPasswordResponseType,
  ResetPasswordResponseType,
  ChangePasswordResponseType,
} from './types';

// Type for error objects with status property
interface ErrorWithStatus {
  status?: number;
}

// API Request Functions
export const adminLoginRequest = async (data: AdminLoginRequestType): Promise<AdminLoginResponseType> => {
  try {
    const transformedData = transformAdminLoginRequest(data);
    const response = await BACKEND_API.post(API_ENDPOINT.auth.login, transformedData);
    return transformAdminLoginResponse(response.data);
  } catch (error) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    throw handleApiError(error as any);
  }
};

export const adminRegisterRequest = async (data: AdminRegisterRequestType): Promise<AdminRegisterResponseType> => {
  try {
    const transformedData = transformAdminRegisterRequest(data);
    const response = await BACKEND_API.post(API_ENDPOINT.auth.register, transformedData);
    return transformAdminRegisterResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const otpVerificationRequest = async (data: OtpVerificationRequestType): Promise<OtpVerificationResponseType> => {
  try {
    const transformedData = transformOtpVerificationRequest(data);
    const response = await BACKEND_API.post(API_ENDPOINT.auth.verifyOtp, transformedData);
    return transformOtpVerificationResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const forgotPasswordRequest = async (data: ForgotPasswordRequestType): Promise<ForgotPasswordResponseType> => {
  try {
    const transformedData = transformForgotPasswordRequest(data);
    const response = await BACKEND_API.post(API_ENDPOINT.auth.forgotPassword, transformedData);
    return transformForgotPasswordResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const resetPasswordRequest = async (data: ResetPasswordRequestType): Promise<ResetPasswordResponseType> => {
  try {
    const transformedData = transformResetPasswordRequest(data);
    const response = await BACKEND_API.post(API_ENDPOINT.auth.resetPassword, transformedData);
    return transformResetPasswordResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const changePasswordRequest = async (data: ChangePasswordRequestType): Promise<ChangePasswordResponseType> => {
  try {
    const transformedData = transformChangePasswordRequest(data);
    const response = await BACKEND_API.post(API_ENDPOINT.auth.changePassword, transformedData);
    return transformChangePasswordResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

// React Query Configuration Objects
export const adminLoginMutation = {
  mutationKey: ['admin-login'],
  mutationFn: adminLoginRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    // Don't retry on authentication errors
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
};

export const adminRegisterMutation = {
  mutationKey: ['admin-register'],
  mutationFn: adminRegisterRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 409) {
      return false;
    }
    return failureCount < 3;
  },
};

export const otpVerificationMutation = {
  mutationKey: ['admin-otp-verification'],
  mutationFn: otpVerificationRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401) {
      return false;
    }
    return failureCount < 3;
  },
};

export const forgotPasswordMutation = {
  mutationKey: ['admin-forgot-password'],
  mutationFn: forgotPasswordRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const resetPasswordMutation = {
  mutationKey: ['admin-reset-password'],
  mutationFn: resetPasswordRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401) {
      return false;
    }
    return failureCount < 3;
  },
};

export const changePasswordMutation = {
  mutationKey: ['admin-change-password'],
  mutationFn: changePasswordRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401) {
      return false;
    }
    return failureCount < 3;
  },
};
