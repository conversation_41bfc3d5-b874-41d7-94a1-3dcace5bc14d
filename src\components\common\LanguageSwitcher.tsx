import { ActionIcon, Menu } from '@mantine/core';
import { IconLanguage } from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { setCookie } from 'cookies-next';
import { LocaleCookie } from '../../data';

export default function LanguageSwitcher() {
  // Since we only support English now, this component can be simplified
  // or removed entirely. For now, we'll just show English as the only option.
  const router = useRouter();
  const currentLocale = router.locale || 'en';

  return (
    <Menu shadow="md" width={120}>
      <Menu.Target>
        <ActionIcon
          variant="default"
          size={36}
          aria-label="Language"
        >
          <IconLanguage size="1.2rem" />
        </ActionIcon>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Item
          style={{
            fontWeight: 'bold',
            backgroundColor: 'var(--mantine-color-blue-light)',
          }}
        >
          English
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
}
