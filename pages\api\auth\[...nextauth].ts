import NextAuth, { NextAuthOptions } from 'next-auth';
import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data/api-endpoints';

interface ExtendedUser {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
  accessToken: string;
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const response = await BACKEND_API.post(API_ENDPOINT.auth.login, {
            email: credentials.email,
            password: credentials.password,
          });

          if (response.data.success && response.data.data.admin) {
            const { admin } = response.data.data;
            const { token } = response.data.data;

            return {
              id: admin.id,
              email: admin.email,
              name: admin.name,
              role: admin.role,
              status: admin.status,
              accessToken: token,
            };
          }

          return null;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          // eslint-disable-next-line no-console
          console.error('Login error:', errorMessage);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user && 'accessToken' in user) {
        const extendedUser = user as ExtendedUser;
        return {
          ...token,
          accessToken: extendedUser.accessToken,
          role: extendedUser.role,
          status: extendedUser.status,
        };
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        return {
          ...session,
          accessToken: token.accessToken as string,
          user: {
            ...session.user,
            id: token.sub || '',
            role: token.role as string,
            status: token.status as string,
          },
        };
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/login',
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
};

export default NextAuth(authOptions);
