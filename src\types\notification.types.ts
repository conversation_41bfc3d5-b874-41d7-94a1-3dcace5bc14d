// Notification System Types for Admin Management
// Extends existing notification types from src/requests/notifications/types.ts

import {
  NotificationType as BaseNotificationType,
  NotificationPriority as BaseNotificationPriority,
  Notification as BaseNotificationInterface,
} from '../requests/notifications/types';

// Extended notification types for admin management
export enum AdminNotificationType {
  // Include all existing types
  SHIPMENT_CREATED = 'SHIPMENT_CREATED',
  SHIPMENT_ASSIGNED_QR = 'SHIPMENT_ASSIGNED_QR',
  SHIPMENT_DROPPED_OFF = 'SHIPMENT_DROPPED_OFF',
  SHIPMENT_PICKED_UP = 'SHIPMENT_PICKED_UP',
  SHIPMENT_IN_TRANSIT = 'SHIPMENT_IN_TRANSIT',
  SHIPMENT_ARRIVED = 'SHIPMENT_ARRIVED',
  SHIPMENT_READY_FOR_DELIVERY = 'SHIPMENT_READY_FOR_DELIVERY',
  SHIPMENT_DELIVERED = 'SHIPMENT_DELIVERED',
  SHIPMENT_CANCELLED = 'SHIPMENT_CANCELLED',
  SHIPMENT_EXPIRED = 'SHIPMENT_EXPIRED',
  QR_CODE_ASSIGNED = 'QR_CODE_ASSIGNED',
  PACKAGE_READY_FOR_PICKUP = 'PACKAGE_READY_FOR_PICKUP',
  DELIVERY_REMINDER = 'DELIVERY_REMINDER',
  SYSTEM_ALERT = 'SYSTEM_ALERT',
  // New admin-specific types
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT',
  BROADCAST_MESSAGE = 'BROADCAST_MESSAGE',
  USER_ACTION_REQUIRED = 'USER_ACTION_REQUIRED',
  PROMOTIONAL = 'PROMOTIONAL',
  MAINTENANCE_ALERT = 'MAINTENANCE_ALERT',
}

// Re-export existing enums for compatibility
export { BaseNotificationType as NotificationType, BaseNotificationPriority as NotificationPriority };

export enum NotificationStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export enum DeliveryStatus {
  PENDING = 'PENDING',
  SENDING = 'SENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  PARTIAL = 'PARTIAL',
}

export enum NotificationErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DELIVERY_ERROR = 'DELIVERY_ERROR',
  TEMPLATE_ERROR = 'TEMPLATE_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
}

// Base notification interface
export interface BaseNotification {
  id: string;
  title: string;
  content: string;
  type: BaseNotificationType;
  priority: BaseNotificationPriority;
  status: NotificationStatus;
  createdAt: string;
  updatedAt: string;
}

// Extended admin notification interface with management fields
export interface AdminNotification extends BaseNotification {
  recipientCount: number;
  deliveredCount: number;
  failedCount: number;
  openedCount: number;
  clickedCount: number;
  deliveryStatus: DeliveryStatus;
  failureReasons?: string[];
  scheduledAt?: string;
  sentAt?: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  templateId?: string;
  variables?: Record<string, string>;
}

// Broadcast message data structure
export interface BroadcastMessageData {
  title: string;
  content: string;
  priority: BaseNotificationPriority;
  recipients: {
    type: 'all' | 'role' | 'individual';
    roles?: string[];
    userIds?: string[];
  };
  scheduleTime?: Date;
  templateId?: string;
  variables?: Record<string, string>;
}

// Broadcast preview interface
export interface BroadcastPreview {
  renderedContent: string;
  recipientCount: number;
  estimatedDeliveryTime: number;
  warnings?: string[];
}

// Template variable definition
export interface TemplateVariable {
  name: string;
  description: string;
  required: boolean;
  defaultValue?: string;
  type: 'text' | 'number' | 'date' | 'boolean';
}

// Notification template interface
export interface NotificationTemplate {
  id: string;
  name: string;
  description?: string;
  subject: string;
  content: string;
  type: BaseNotificationType;
  variables: TemplateVariable[];
  isActive: boolean;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    name: string;
  };
}

// Template data for creation/update
export interface NotificationTemplateData {
  name: string;
  description?: string;
  subject: string;
  content: string;
  type: BaseNotificationType;
  variables: TemplateVariable[];
  isActive?: boolean;
}

// Recipient option for broadcast selection
export interface RecipientOption {
  id: string;
  name: string;
  type: 'user' | 'role';
  count?: number;
}

// Notification analytics interfaces
export interface NotificationAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  totalOpened: number;
  totalClicked: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  failureReasons: Array<{
    reason: string;
    count: number;
    percentage: number;
  }>;
  performanceByType: Array<{
    type: BaseNotificationType;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
}

// Date range interface for analytics
export interface DateRange {
  startDate: Date;
  endDate: Date;
}

// Notification filters
export interface NotificationFilters {
  search?: string;
  status?: NotificationStatus;
  type?: BaseNotificationType;
  priority?: BaseNotificationPriority;
  createdBy?: string;
  dateRange?: DateRange;
  deliveryStatus?: DeliveryStatus;
}

// Analytics filters
export interface AnalyticsFilters {
  dateRange: DateRange;
  notificationType?: BaseNotificationType;
  recipientType?: 'all' | 'role' | 'individual';
  priority?: BaseNotificationPriority;
}

// Pagination state
export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Notification error interface
export interface NotificationError {
  type: NotificationErrorType;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
}