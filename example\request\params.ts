import { NextApiRequest } from 'next';

// eslint-disable-next-line complexity
export const returnPaymentMethodsParams = (req: NextApiRequest) => {
  const params = req.query;

  let filterIndex = 0;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const filters: Record<string, any> = {};

  if (params?.search) {
    filters['filters[$and][0][$or][0][id][$containsi]'] = params.search;
    filters['filters[$and][0][$or][1][title][$containsi]'] = params.search;
    filterIndex += 1;
  }
  const badgeValues = ([] as string[]).concat(params['badges[]'] || params.badges || []).filter(Boolean);
  const tagValues = ([] as string[]).concat(params['tags[]'] || params.tags || []).filter(Boolean);

  if (badgeValues.length > 0) {
    badgeValues.forEach((badge, idx) => {
      filters[`filters[$and][${filterIndex}][$or][${idx}][badges][id][$eq]`] = badge;
    });
    filterIndex += 1;
  }

  if (tagValues.length > 0) {
    tagValues.forEach((tag, idx) => {
      filters[`filters[$and][${filterIndex}][$or][${idx}][tag][$containsi]`] = tag;
    });
    filterIndex += 1;
  }

  return {
    'pagination[page]': params.page,
    'pagination[pageSize]': params.pageSize,
    'pagination[start]': params.start,
    'pagination[limit]': params.limit,
    'populate[badges][populate]': '*',
    'populate[deposit_currencies][populate]':
      params.depositCurrencies === 'true' ? '*' : null,
    'populate[withdraw_currencies][populate]':
      params.withdrawCurrencies === 'true' ? '*' : null,
    'populate[icon][populate]': params.icon === 'true' ? '*' : null,
    'populate[deposit_custom_fields][populate]':
      params.customFields === 'true' ? '*' : null,
    'populate[withdraw_custom_fields][populate]':
      params.customFields === 'true' ? '*' : null,
    'filters[show_in_rates_bar][$eq]':
      params.showInRatesBar === 'true' ? true : null,
    'filters[deposit_currencies][uid][$eq]': params?.depositCurrencyId,
    'filters[withdraw_currencies][uid][$eq]': params?.withdrawCurrencyId,
    ...filters,
    'filters[createdAt][$gte]':
      typeof params?.createdAtGte === 'string' && params?.createdAtGte !== ''
        ? params?.createdAtGte
        : null,
    'filters[createdAt][$lte]':
      typeof params?.createdAtLte === 'string' && params?.createdAtLte !== ''
        ? params?.createdAtLte
        : null,
    publicationState: 'preview',
    sort: params.sort,
  };
};

// This map is used to convert front-end keys to back-end keys for the sort parameter
export const sortKeysMapping = new Map<string, string>([
  ['label', 'title'],
  ['showInRatesBar', 'show_in_rates_bar'],
]);
