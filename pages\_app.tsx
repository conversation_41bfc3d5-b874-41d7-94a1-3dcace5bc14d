/* eslint-disable @typescript-eslint/no-explicit-any */
import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '../styles/ltr-pin-input.css';
import '../src/utils/date-format';
import Head from 'next/head';
import Providers from '../src/components/common/Providers';
import { useRouter } from 'next/router';
import type { AppProps } from 'next/app';
import { SessionProvider } from 'next-auth/react';
import AdminLayout from '../src/components/layouts/AdminLayout';
import ClientOnly from '../src/components/common/ClientOnly';
import FullScreenLoader from '../src/components/common/FullScreenLoader';
import { AdminAuthProvider } from '../src/contexts/AdminAuthContext';
import { LoadingProvider, useLoading } from '../src/contexts/LoadingContext';
import { AdminRouteGuard } from '../src/components/auth/AdminRouteGuard';
import { MaintenancePage } from '../src/components/maintenance-page';

const noLayoutRoutes = ['/', '/auth/login', '/auth/register', '/auth/verify-otp', '/auth/forgot-password', '/auth/reset-password'];

function AppWithLoading({ Component, pageProps, shouldUseLayout }: {
  Component: any;
  pageProps: any;
  shouldUseLayout: boolean;
}) {
  const { isLoading, loadingText } = useLoading();

  return (
    <FullScreenLoader visible={isLoading} text={loadingText}>
      <ClientOnly
        fallback={(
          <FullScreenLoader
            visible
            text="Initializing admin application..."
          />
        )}
      >
        <AdminRouteGuard>
          {shouldUseLayout ? (
            <AdminLayout>
              <Component {...pageProps} />
            </AdminLayout>
          ) : (
            <Component {...pageProps} />
          )}
        </AdminRouteGuard>
      </ClientOnly>
    </FullScreenLoader>
  );
}

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const shouldUseLayout = !noLayoutRoutes.includes(router.pathname);

  if (
    process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true'
    // && (url === '' || url === '')
  ) {
    return (
      <Providers>
        <MaintenancePage />
      </Providers>
    );
  }

  return (
    <SessionProvider session={pageProps.session}>
      <AdminAuthProvider>
        <Providers>
          <Head>
            <title>NAQALAT Admin</title>
            <meta
              name="viewport"
              content="minimum-scale=1, initial-scale=1, width=device-width, user-scalable=no"
            />
            <link rel="shortcut icon" href="/favicon.svg" />
            <style>
              {`
          /* Font configuration - English only */
          @import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@200;300;400;500;600;700;800;900&display=swap');

          /* Font classes */
          .font-english, .font-english * {
            font-family: 'Roboto Condensed', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
          }

          /* Default font for all elements */
          html, html *, [dir="ltr"], [dir="ltr"] * {
            font-family: 'Roboto Condensed', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
          }

          /* Force font on all elements */
          * {
            font-family: inherit !important;
          }

          /* LTR support */
          [dir="ltr"] {
            text-align: left;
          }

          [dir="ltr"] .mantine-Group-root {
            flex-direction: row;
          }

          /* Ensure header and menus appear above all content including maps */
          .mantine-AppShell-header {
            z-index: 9999 !important;
          }

          .mantine-AppShell-navbar {
            z-index: 9998 !important;
          }

          .mantine-Menu-dropdown {
            z-index: 10000 !important;
          }

          .mantine-Popover-dropdown {
            z-index: 10001 !important;
          }

          /* Specifically target notification dropdown */
          .mantine-Popover-dropdown[data-notification-dropdown] {
            z-index: 10500 !important;
            position: fixed !important;
            max-height: 80vh !important;
            overflow: hidden !important;
          }

          /* Ensure notification dropdown appears above header */
          [data-notification-dropdown] {
            z-index: 10500 !important;
          }

          /* Leaflet map containers should have lower z-index */
          .leaflet-container {
            z-index: 1 !important;
          }

          .leaflet-control-container {
            z-index: 2 !important;
          }

          /* Mantine notifications should be above everything */
          .mantine-Notifications-root {
            z-index: 11000 !important;
          }

          /* Ensure tooltips appear above other elements */
          .mantine-Tooltip-tooltip {
            z-index: 10600 !important;
          }

          /* Leaflet popup content transparent background */
          .leaflet-popup-content-wrapper {
            width: 420px !important;
            background-color: black !important;
            opacity: 0.9 !important;
            color: white !important;
          }
        `}
            </style>
          </Head>
          <LoadingProvider>
            <AppWithLoading
              Component={Component}
              pageProps={pageProps}
              shouldUseLayout={shouldUseLayout}
            />
          </LoadingProvider>
        </Providers>
      </AdminAuthProvider>
    </SessionProvider>
  );
}
