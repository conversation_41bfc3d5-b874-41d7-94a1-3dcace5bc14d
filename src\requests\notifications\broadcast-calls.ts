import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  SendBroadcastRequest,
  SendBroadcastResponse,
  ScheduleBroadcastRequest,
  ScheduleBroadcastResponse,
  PreviewBroadcastRequest,
  PreviewBroadcastResponse,
  GetRecipientOptionsRequest,
  GetRecipientOptionsResponse,
} from '../../types/notification-api.types';
import {
  transformSendBroadcastRequest,
  transformScheduleBroadcastRequest,
  transformPreviewBroadcastRequest,
  transformGetRecipientOptionsParams,
} from './broadcast-request-transformer';
import {
  transformSendBroadcastResponse,
  transformScheduleBroadcastResponse,
  transformPreviewBroadcastResponse,
  transformGetRecipientOptionsResponse,
} from './broadcast-response-transformer';

// Use centralized query keys
const queryKeys = QUERY_KEYS.notifications;

/**
 * @description Send broadcast message immediately
 * @param data - Broadcast message data
 * @returns broadcast response with notification ID and recipient count
 */
const sendBroadcastRequest = (data: SendBroadcastRequest) => {
  const transformedData = transformSendBroadcastRequest(data);

  return CLIENT_API.post(API_ENDPOINT.notifications.broadcast.send, transformedData)
    .then((res) => transformSendBroadcastResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Schedule broadcast message for future delivery
 * @param data - Broadcast message data with schedule time
 * @returns scheduled broadcast response
 */
const scheduleBroadcastRequest = (data: ScheduleBroadcastRequest) => {
  const transformedData = transformScheduleBroadcastRequest(data);

  return CLIENT_API.post(API_ENDPOINT.notifications.broadcast.schedule, transformedData)
    .then((res) => transformScheduleBroadcastResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Preview broadcast message before sending
 * @param data - Broadcast preview data
 * @returns preview with rendered content and recipient count
 */
const previewBroadcastRequest = (data: PreviewBroadcastRequest) => {
  const transformedData = transformPreviewBroadcastRequest(data);

  return CLIENT_API.post(API_ENDPOINT.notifications.broadcast.preview, transformedData)
    .then((res) => transformPreviewBroadcastResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Get available recipient options for broadcast selection
 * @param params - Search and filter parameters
 * @returns users and roles available for selection
 */
const getRecipientOptionsRequest = (params: GetRecipientOptionsRequest = {}) => {
  const queryParams = transformGetRecipientOptionsParams(params);
  
  const urlParams = new URLSearchParams();
  if (queryParams && typeof queryParams === 'object') {
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlParams.append(key, String(value));
      }
    });
  }

  const queryString = urlParams.toString();
  const url = queryString 
    ? `${API_ENDPOINT.notifications.broadcast.recipients}?${queryString}` 
    : API_ENDPOINT.notifications.broadcast.recipients;

  return CLIENT_API.get(url)
    .then((res) => transformGetRecipientOptionsResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Validate recipient selection before sending broadcast
 * @param recipients - Recipient selection data
 * @returns validation result with recipient count and warnings
 */
const validateRecipientsRequest = (recipients: SendBroadcastRequest['recipients']) => {
  return CLIENT_API.post(API_ENDPOINT.notifications.broadcast.validateRecipients, recipients)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// React Query configurations for broadcast operations
export const getRecipientOptionsQuery = (params: GetRecipientOptionsRequest = {}) => ({
  queryKey: [queryKeys.broadcast, 'recipients', params],
  queryFn: () => getRecipientOptionsRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 300000, // 5 minutes - recipient options don't change frequently
});

// Mutation functions for broadcast operations
export const sendBroadcastMutation = () => ({
  mutationKey: [queryKeys.broadcast, 'send'],
  mutationFn: (data: SendBroadcastRequest) => sendBroadcastRequest(data),
});

export const scheduleBroadcastMutation = () => ({
  mutationKey: [queryKeys.broadcast, 'schedule'],
  mutationFn: (data: ScheduleBroadcastRequest) => scheduleBroadcastRequest(data),
});

export const previewBroadcastMutation = () => ({
  mutationKey: [queryKeys.broadcast, 'preview'],
  mutationFn: (data: PreviewBroadcastRequest) => previewBroadcastRequest(data),
});

export const validateRecipientsMutation = () => ({
  mutationKey: [queryKeys.broadcast, 'validateRecipients'],
  mutationFn: (recipients: SendBroadcastRequest['recipients']) => validateRecipientsRequest(recipients),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Send broadcast mutation with auto-invalidation
export const sendBroadcastMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...sendBroadcastMutation(),
    onSuccess: async () => {
      // Invalidate notifications list after sending broadcast
      await cacheManager.invalidateAfterNotificationUpdate();
    },
  };
};

// Schedule broadcast mutation with auto-invalidation
export const scheduleBroadcastMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...scheduleBroadcastMutation(),
    onSuccess: async () => {
      // Invalidate notifications list after scheduling broadcast
      await cacheManager.invalidateAfterNotificationUpdate();
    },
  };
};

// Export request functions for direct use
export {
  sendBroadcastRequest,
  scheduleBroadcastRequest,
  previewBroadcastRequest,
  getRecipientOptionsRequest,
  validateRecipientsRequest,
};