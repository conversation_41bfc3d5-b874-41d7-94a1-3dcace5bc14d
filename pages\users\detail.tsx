/* eslint-disable no-underscore-dangle */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable max-lines */
/* eslint-disable complexity */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Group,
  Text,
  Badge,
  Button,
  Stack,
  Grid,
  Card,

  Alert,
  Modal,
  Textarea,
  Avatar,
  Divider,
  Paper,
  Box,
  Flex,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconUserCheck,
  IconUserX,
  IconShieldCheck,
  IconShieldX,
  IconAlertCircle,
  IconUser,
  IconMail,
  IconPhone,
  IconCalendar,
  IconEye,
} from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useSession } from 'next-auth/react';
import { useLoading } from '../../src/contexts/LoadingContext';
import {
  getUserDetailRequest,
  changeUserStatusMutation,
  approveUserMutation,
  changeOperatorApprovalMutation,
} from '../../src/requests/admin-users';
import {
  UserStatus,
  ApprovalStatus,
} from '../../src/types/admin.types';
import { ErrorBackendType } from '../../src/types/error.type';

// Type for status changes - only ACTIVE and SUSPENDED are allowed for status changes
type UserStatusChange = 'ACTIVE' | 'SUSPENDED';

function UserDetailPageContent() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { status: sessionStatus } = useSession();
  const { showLoading, hideLoading } = useLoading();
  const { id } = router.query;
  const userId = Array.isArray(id) ? id[0] : id;

  const [statusModalOpened, { open: openStatusModal, close: closeStatusModal }] = useDisclosure(false);
  const [approvalModalOpened, { open: openApprovalModal, close: closeApprovalModal }] = useDisclosure(false);
  const [statusReason, setStatusReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [newStatus, setNewStatus] = useState<UserStatusChange | ''>('');
  const [newApprovalStatus, setNewApprovalStatus] = useState<ApprovalStatus | ''>('');

  // Handle authentication
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/login');
    }
  }, [sessionStatus, router]);

  // Queries and mutations
  const {
    data: userData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['admin-user-detail', userId], // Clean, specific key for dedicated endpoint
    queryFn: () => {
      // eslint-disable-next-line no-console
      console.log('Fetching user detail for userId:', userId); // Debug log
      return getUserDetailRequest(userId || '');
    },
    enabled: !!userId && router.isReady,
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache the data
    refetchOnWindowFocus: false,
    refetchOnMount: true, // Always refetch on mount
    retry: (failureCount: number, retryError: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
      if (retryError?.status === 401 || retryError?.status === 403 || retryError?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Use global loading for this query
  useEffect(() => {
    if (isLoading && userId) {
      showLoading('Loading user details...');
    } else {
      hideLoading();
    }
  }, [isLoading, userId, showLoading, hideLoading]);

  // Force refetch when userId changes
  useEffect(() => {
    if (userId && router.isReady) {
      // Clear all user detail queries to prevent stale data
      queryClient.removeQueries({ queryKey: ['admin-user-detail'] });

      // Clear any cached data for this specific user
      queryClient.invalidateQueries({ queryKey: ['admin-user-detail', userId] });

      // Force refetch immediately since we have a dedicated endpoint
      refetch();
    }
  }, [userId, router.isReady, queryClient, refetch]);

  const statusMutation = useMutation({
    ...changeUserStatusMutation,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'User status updated successfully',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-user-detail', userId] });
      queryClient.invalidateQueries({ queryKey: ['admin-users-list'] });
      closeStatusModal();
      setStatusReason('');
      setNewStatus('');
    },
    onError: (mutationError: ErrorBackendType) => {
      notifications.show({
        title: 'Error',
        message: mutationError?.response?.data?.error?.message || 'Failed to update user status',
        color: 'red',
      });
    },
  });

  const approvalMutation = useMutation({
    ...approveUserMutation,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'User approval status updated successfully',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-user-detail', userId] });
      queryClient.invalidateQueries({ queryKey: ['admin-users-list'] });
      closeApprovalModal();
      setApprovalNotes('');
      setNewApprovalStatus('');
    },
    onError: (mutationError: ErrorBackendType) => {
      notifications.show({
        title: 'Error',
        message: mutationError?.response?.data?.error?.message || 'Failed to update approval status',
        color: 'red',
      });
    },
  });

  const approvalToggleMutation = useMutation({
    ...changeOperatorApprovalMutation,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'Approval status updated successfully',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-user-detail', userId] });
      queryClient.invalidateQueries({ queryKey: ['admin-users-list'] });
      closeApprovalModal();
      setApprovalNotes('');
      setNewApprovalStatus('');
    },
    onError: (mutationError: ErrorBackendType) => {
      notifications.show({
        title: 'Error',
        message: mutationError?.response?.data?.error?.message || 'Failed to update approval status',
        color: 'red',
      });
    },
  });

  // Event handlers
  const handleStatusChange = () => {
    if (!newStatus || !userId) return;
    statusMutation.mutate({
      userId,
      status: newStatus,
      reason: statusReason.trim() || `Status changed to ${newStatus} by admin`,
    });
  };

  const openStatusChangeModal = (status: UserStatusChange) => {
    setNewStatus(status);
    openStatusModal();
  };

  const openApprovalChangeModal = (approval: ApprovalStatus) => {
    setNewApprovalStatus(approval);
    openApprovalModal();
  };

  // Helper functions
  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE:
        return 'green';
      case UserStatus.SUSPENDED:
        return 'red';
      case UserStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  const getApprovalStatusColor = (status?: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.APPROVED:
        return 'green';
      case ApprovalStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  // Helper function to get the actual approval status for the user
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getUserApprovalStatus = (userInfo:any): ApprovalStatus => {
    if (userInfo.user_type === 'CUSTOMER') {
      return ApprovalStatus.APPROVED; // Customers are auto-approved
    }

    if (userInfo.user_type === 'ACCESS_OPERATOR') {
      return userInfo.accessOperator?.approved ? ApprovalStatus.APPROVED : ApprovalStatus.PENDING;
    }

    if (userInfo.user_type === 'CAR_OPERATOR') {
      return userInfo.carOperator?.approved ? ApprovalStatus.APPROVED : ApprovalStatus.PENDING;
    }

    return ApprovalStatus.PENDING;
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  // Don't render anything while loading - global loading will handle it
  if (isLoading) {
    return null;
  }

  if (error || !userData?.data.user) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading user"
          color="red"
          variant="light"
        >
          <Text mb="md">
            Failed to load user data. Please try again.
          </Text>
          <Group>
            <Button variant="outline" onClick={() => refetch()}>
              Retry
            </Button>
            <Button variant="outline" onClick={() => router.back()}>
              Go Back
            </Button>
          </Group>
        </Alert>
      </Container>
    );
  }

  const { user } = userData!.data;

  // Helper to check if user is currently approved
  const isApproved = (user.user_type === 'ACCESS_OPERATOR' && user.accessOperator?.approved)
    || (user.user_type === 'CAR_OPERATOR' && user.carOperator?.approved);

  // Enhanced approval handler that works for both approval types
  const handleApprovalChangeEnhanced = () => {
    if (!newApprovalStatus || !userId) return;

    // For Access Operators and Car Operators, use the new approval system
    if (user.user_type === 'ACCESS_OPERATOR' || user.user_type === 'CAR_OPERATOR') {
      const approved = newApprovalStatus === ApprovalStatus.APPROVED;
      approvalToggleMutation.mutate({
        userId,
        approved,
        notes: approvalNotes.trim() || `Approval ${approved ? 'granted' : 'revoked'} by admin`,
      });
    } else {
      // For other user types, use the old approval system
      if (newApprovalStatus !== ApprovalStatus.APPROVED) {
        return;
      }
      approvalMutation.mutate({
        userId,
        approval_status: newApprovalStatus,
        notes: approvalNotes.trim() || `Approval status changed to ${newApprovalStatus} by admin`,
      });
    }
  };

  return (
    <Container size="xl" py="xl" key={`${router.asPath}-${userId}`}>
      {/* Header */}
      <Paper withBorder p="lg" mb="xl">
        <Group justify="space-between" align="flex-start">
          <Group>
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size="1rem" />}
              onClick={() => router.back()}
            >
              Back to Users
            </Button>
            <Divider orientation="vertical" />
            <Box>
              <Title order={1} mb="xs">User Details</Title>
              <Text size="sm" c="dimmed">
                Manage user information and approval status
              </Text>
            </Box>
          </Group>

          <Flex gap="md" wrap="wrap">
            {/* Status Actions */}
            {user.status === UserStatus.ACTIVE ? (
              <Button
                color="red"
                variant="light"
                leftSection={<IconUserX size="1rem" />}
                onClick={() => openStatusChangeModal('SUSPENDED')}
                loading={statusMutation.isPending}
              >
                Deactivate User
              </Button>
            ) : (
              <Button
                color="green"
                variant="light"
                leftSection={<IconUserCheck size="1rem" />}
                onClick={() => openStatusChangeModal('ACTIVE')}
                loading={statusMutation.isPending}
              >
                Activate User
              </Button>
            )}

            {/* Approval Toggle - Only show for non-customer users */}
            {user.user_type !== 'CUSTOMER' && (
              <Button
                color={isApproved ? 'red' : 'green'}
                variant="light"
                leftSection={isApproved ? <IconShieldX size="1rem" /> : <IconShieldCheck size="1rem" />}
                onClick={() => openApprovalChangeModal(isApproved ? ApprovalStatus.PENDING : ApprovalStatus.APPROVED)}
                loading={approvalMutation.isPending || approvalToggleMutation.isPending}
              >
                {isApproved ? 'Revoke Approval' : 'Approve User'}
              </Button>
            )}
          </Flex>
        </Group>
      </Paper>

      <Grid>
        {/* User Profile Card */}
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Card withBorder shadow="sm" p="xl">
            {/* Profile Header */}
            <Group mb="xl">
              <Avatar size="xl" radius="md" color="blue">
                {user.name?.charAt(0).toUpperCase() || 'U'}
              </Avatar>
              <Box flex={1}>
                <Title order={2} mb="xs">{user.name}</Title>
                <Group gap="xs" mb="sm">
                  <IconMail size="1rem" />
                  <Text c="dimmed">{user.email}</Text>
                </Group>
                {user.phone && (
                  <Group gap="xs">
                    <IconPhone size="1rem" />
                    <Text c="dimmed">{user.phone}</Text>
                  </Group>
                )}
              </Box>
            </Group>

            <Divider mb="xl" />

            {/* Basic Information */}
            <Box mb="xl">
              <Group mb="md">
                <IconUser size="1.2rem" />
                <Title order={3}>Basic Information</Title>
              </Group>

              <Grid>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">User Type</Text>
                  <Badge variant="light" color="blue" size="lg" fullWidth>
                    {user.user_type.replace('_', ' ')}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">Account Status</Text>
                  <Badge color={getStatusColor(user.status as UserStatus)} size="lg" fullWidth>
                    {user.status}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">Approval Status</Text>
                  <Badge
                    color={getApprovalStatusColor(getUserApprovalStatus(user))}
                    size="lg"
                    fullWidth
                    variant="filled"
                  >
                    {getUserApprovalStatus(user)}
                  </Badge>
                </Grid.Col>
              </Grid>
            </Box>

            {/* Additional Information */}
            <Box mb="xl">
              <Group mb="md">
                <IconEye size="1.2rem" />
                <Title order={3}>Account Details</Title>
              </Group>

              <Grid>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">Email Verified</Text>
                  {(() => {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const emailVerified = (user as any).email_verified;
                    return (
                      <Badge
                        color={emailVerified ? 'green' : 'red'}
                        size="lg"
                        fullWidth
                        variant="filled"
                      >
                        {emailVerified ? 'Verified' : 'Not Verified'}
                      </Badge>
                    );
                  })()}
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">Total Shipments</Text>
                  <Text fw={500} size="lg">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(user as any)._count?.shipments || 0}
                  </Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">Audit Logs</Text>
                  <Text fw={500} size="lg">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(user as any)._count?.auditLogs || 0}
                  </Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">Email Verifications</Text>
                  <Text fw={500} size="lg">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(user as any)._count?.emailVerifications || 0}
                  </Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">User Verifications</Text>
                  <Text fw={500} size="lg">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(user as any)._count?.userVerifications || 0}
                  </Text>
                </Grid.Col>
              </Grid>
            </Box>
          </Card>
        </Grid.Col>

        {/* Status Summary */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Stack gap="md">
            {/* Business Information - Only for AO and CO users */}
            {/* Note: Future API fields that could be added:
                - For AO: geoLatitude, geoLongitude for location display
                - For CO: chosenAccessOperators array for selected AOs */}
            {(user.user_type === 'ACCESS_OPERATOR' || user.user_type === 'CAR_OPERATOR') && (
              <Card withBorder shadow="sm" p="lg">
                <Group mb="lg">
                  <IconUser size="1.2rem" />
                  <Title order={3}>Business Information</Title>
                </Group>

                <Stack gap="md">
                  {user.user_type === 'ACCESS_OPERATOR' && user.accessOperator && (
                    <>
                      <Box>
                        <Text size="sm" fw={500} c="dimmed" mb="xs">Business Name</Text>
                        <Text fw={500}>
                          {user.accessOperator.business_name || user.profile?.business_name || 'Not provided'}
                        </Text>
                      </Box>

                      <Box>
                        <Text size="sm" fw={500} c="dimmed" mb="xs">Business Address</Text>
                        <Text fw={500}>
                          {user.accessOperator.address || user.profile?.business_address || 'Not provided'}
                        </Text>
                      </Box>

                      {user.profile?.business_license && (
                        <Box>
                          <Text size="sm" fw={500} c="dimmed" mb="xs">Business License</Text>
                          <Text fw={500}>{user.profile.business_license}</Text>
                        </Box>
                      )}

                      {user.profile?.operating_hours && (
                        <Box>
                          <Text size="sm" fw={500} c="dimmed" mb="xs">Operating Hours</Text>
                          <Text fw={500}>{user.profile.operating_hours}</Text>
                        </Box>
                      )}

                      {/* Geo Location for Access Operators */}
                      {(() => {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const aoData = user.accessOperator as any;
                        return aoData?.geo_latitude && aoData?.geo_longitude ? (
                          <Box>
                            <Text size="sm" fw={500} c="dimmed" mb="xs">Location</Text>
                            <Text fw={500}>
                              Lat:
                              {aoData.geo_latitude}
                              , Lng:
                              {aoData.geo_longitude}
                            </Text>
                            <Text size="xs" c="dimmed">
                              <a
                                href={`https://maps.google.com/?q=${aoData.geo_latitude},${aoData.geo_longitude}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: 'var(--mantine-color-blue-6)' }}
                              >
                                View on Google Maps
                              </a>
                            </Text>
                          </Box>
                        ) : null;
                      })()}

                      {/* Creation and Update Info */}
                      {(() => {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const aoData = user.accessOperator as any;
                        return aoData?.created_at ? (
                          <Box>
                            <Text size="sm" fw={500} c="dimmed" mb="xs">Operator Registration</Text>
                            <Text fw={500} size="sm">
                              {new Date(aoData.created_at).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </Text>
                          </Box>
                        ) : null;
                      })()}
                    </>
                  )}

                  {user.user_type === 'CAR_OPERATOR' && user.carOperator && (
                    <>
                      <Box>
                        <Text size="sm" fw={500} c="dimmed" mb="xs">Business Name</Text>
                        <Text fw={500}>
                          {user.carOperator.business_name || user.profile?.business_name || 'Not provided'}
                        </Text>
                      </Box>

                      <Box>
                        <Text size="sm" fw={500} c="dimmed" mb="xs">Business Address</Text>
                        <Text fw={500}>
                          {user.carOperator.address || user.profile?.business_address || 'Not provided'}
                        </Text>
                      </Box>

                      {user.profile?.business_license && (
                        <Box>
                          <Text size="sm" fw={500} c="dimmed" mb="xs">Business License</Text>
                          <Text fw={500}>{user.profile.business_license}</Text>
                        </Box>
                      )}

                      {user.profile?.operating_hours && (
                        <Box>
                          <Text size="sm" fw={500} c="dimmed" mb="xs">Operating Hours</Text>
                          <Text fw={500}>{user.profile.operating_hours}</Text>
                        </Box>
                      )}

                      {/* Creation and Update Info */}
                      {(() => {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const coData = user.carOperator as any;
                        return coData?.created_at ? (
                          <Box>
                            <Text size="sm" fw={500} c="dimmed" mb="xs">Operator Registration</Text>
                            <Text fw={500} size="sm">
                              {new Date(coData.created_at).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </Text>
                          </Box>
                        ) : null;
                      })()}

                      <Box>
                        <Text size="sm" fw={500} c="dimmed" mb="xs">Operator Status</Text>
                        <Badge
                          color={user.carOperator.approved ? 'green' : 'orange'}
                          variant="filled"
                        >
                          {user.carOperator.approved ? 'Approved' : 'Pending Approval'}
                        </Badge>
                      </Box>
                    </>
                  )}

                  {/* Show message if no business info is available */}
                  {((user.user_type === 'ACCESS_OPERATOR' && !user.accessOperator)
                    || (user.user_type === 'CAR_OPERATOR' && !user.carOperator)) && (
                    <Text c="dimmed" fs="italic">
                      No business information available
                    </Text>
                  )}
                </Stack>
              </Card>
            )}

            {/* Statistics - Only for AO and CO users with statistics */}
            {(user.user_type === 'ACCESS_OPERATOR' || user.user_type === 'CAR_OPERATOR') && user.statistics && (
              <Card withBorder shadow="sm" p="lg">
                <Group mb="lg">
                  <IconEye size="1.2rem" />
                  <Title order={3}>Performance Statistics</Title>
                </Group>

                <Stack gap="md">
                  <Box>
                    <Text size="sm" fw={500} c="dimmed" mb="xs">Total Shipments</Text>
                    <Text fw={500} size="lg">{user.statistics.total_shipments}</Text>
                  </Box>

                  <Box>
                    <Text size="sm" fw={500} c="dimmed" mb="xs">Completed Shipments</Text>
                    <Text fw={500} size="lg">{user.statistics.completed_shipments}</Text>
                  </Box>

                  <Box>
                    <Text size="sm" fw={500} c="dimmed" mb="xs">Success Rate</Text>
                    <Badge
                      color={(() => {
                        const rate = user.statistics.success_rate;
                        if (rate >= 90) return 'green';
                        if (rate >= 70) return 'yellow';
                        return 'red';
                      })()}
                      variant="filled"
                      size="lg"
                    >
                      {user.statistics.success_rate.toFixed(1)}
                      %
                    </Badge>
                  </Box>
                </Stack>
              </Card>
            )}

            {/* Account Timeline */}
            <Card withBorder shadow="sm" p="lg">
              <Group mb="lg">
                <IconCalendar size="1.2rem" />
                <Title order={3}>Account Timeline</Title>
              </Group>

              <Stack gap="lg">
                <Paper withBorder p="md">
                  <Text size="sm" fw={500} c="blue" mb="xs">Account Created</Text>
                  <Text fw={500}>{formatDate(user.created_at)}</Text>
                </Paper>

                {user.updated_at && (
                  <Paper withBorder p="md">
                    <Text size="sm" fw={500} c="green" mb="xs">Last Updated</Text>
                    <Text fw={500}>{formatDate(user.updated_at)}</Text>
                  </Paper>
                )}

                {user.last_login && (
                  <Paper withBorder p="md" bg="var(--mantine-color-orange-0)">
                    <Text size="sm" fw={500} c="orange" mb="xs">Last Login</Text>
                    <Text fw={500}>{formatDate(user.last_login)}</Text>
                  </Paper>
                )}
              </Stack>
            </Card>

            {/* Quick Actions */}
            <Card withBorder shadow="sm" p="lg">
              <Title order={4} mb="md">Quick Actions</Title>
              <Stack gap="sm">
                <Button
                  variant="light"
                  fullWidth
                  leftSection={<IconEye size="1rem" />}
                  onClick={() => { /* TODO: View user activity */ }}
                >
                  View Activity Log
                </Button>
                <Button
                  variant="light"
                  color="orange"
                  fullWidth
                  leftSection={<IconMail size="1rem" />}
                  onClick={() => { /* TODO: Send notification */ }}
                >
                  Send Notification
                </Button>
              </Stack>
            </Card>
          </Stack>
        </Grid.Col>
      </Grid>

      {/* Status Change Modal */}
      <Modal
        opened={statusModalOpened}
        onClose={closeStatusModal}
        title={`Change User Status to ${newStatus}`}
        size="md"
      >
        <Stack gap="md">
          <Text>
            Are you sure you want to change this user&apos;s status to
            {' '}
            <Text component="span" fw={500} c={newStatus === 'ACTIVE' ? 'green' : 'red'}>
              {newStatus}
            </Text>
            ?
          </Text>

          <Textarea
            label="Reason (optional)"
            placeholder="Enter reason for status change..."
            value={statusReason}
            onChange={(e) => setStatusReason(e.target.value)}
            rows={3}
          />

          <Group justify="flex-end">
            <Button variant="outline" onClick={closeStatusModal}>
              Cancel
            </Button>
            <Button
              color={newStatus === 'ACTIVE' ? 'green' : 'red'}
              onClick={handleStatusChange}
              loading={statusMutation.isPending}
            >
              Confirm Change
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Approval Change Modal */}
      <Modal
        opened={approvalModalOpened}
        onClose={closeApprovalModal}
        title={newApprovalStatus === ApprovalStatus.APPROVED ? 'Approve User' : 'Revoke Approval'}
        size="md"
      >
        <Stack gap="md">
          <Text>
            Are you sure you want to
            {' '}
            <Text
              component="span"
              fw={500}
              c={newApprovalStatus === ApprovalStatus.APPROVED ? 'green' : 'red'}
            >
              {newApprovalStatus === ApprovalStatus.APPROVED ? 'approve' : 'revoke approval for'}
            </Text>
            {' '}
            this user?
          </Text>

          <Textarea
            label="Notes (optional)"
            placeholder={`Enter notes for this ${newApprovalStatus === ApprovalStatus.APPROVED ? 'approval' : 'revocation'}...`}
            value={approvalNotes}
            onChange={(e) => setApprovalNotes(e.target.value)}
            rows={3}
          />

          <Group justify="flex-end">
            <Button variant="outline" onClick={closeApprovalModal}>
              Cancel
            </Button>
            <Button
              color={newApprovalStatus === ApprovalStatus.APPROVED ? 'green' : 'red'}
              onClick={handleApprovalChangeEnhanced}
              loading={approvalMutation.isPending || approvalToggleMutation.isPending}
            >
              {newApprovalStatus === ApprovalStatus.APPROVED ? 'Confirm Approval' : 'Confirm Revocation'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}

// Wrapper component that forces re-mounting when userId changes
export default function UserDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const userId = Array.isArray(id) ? id[0] : id;

  // Don't render anything until router is ready and we have a userId
  if (!router.isReady || !userId) {
    return null;
  }

  // Use userId as key to force complete component remount when it changes
  return <UserDetailPageContent key={userId} />;
}
