/**
 * This component renders payment method details page and form to update payment method data.
 */
import { Text, LoadingOverlay } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import Head from 'next/head';
import { useRouter } from 'next/router';

import Layout from '@/src/components/layout';
import { getPaymentMethodQuery } from '@/src/store/wallet/payment-methods';
import PaymentMethodForm from '@/src/components/wallet/payment-method/payment-method-form';
import { PageProps } from '@/src/types/page-prop.type';

export default function Currency({ authenticated }: PageProps) {
  // get payment method id from page route to get its data.
  const { query } = useRouter();
  const id = query.index;
  /**
   * fetch payment method data by id.
   * @returns payment method data
   */
  const { data, isLoading } = useQuery(
    getPaymentMethodQuery(`${id}`, { isAuth: authenticated }),
  );

  return (
    <div>
      <Head>
        <title>Payment Method | Admin</title>
      </Head>
      <Layout>
        <Text size="xl" fw={500} c="gray">
          ID
          {' : '}
          {data?.id}
        </Text>

        <PaymentMethodForm data={data} authenticated={authenticated} />
        <LoadingOverlay visible={isLoading} overlayProps={{ blur: 2 }} />
      </Layout>
    </div>
  );
}
