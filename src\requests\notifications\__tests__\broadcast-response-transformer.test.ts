import { describe, it, expect } from '@jest/globals';
import {
  transformSendBroadcastResponse,
  transformScheduleBroadcastResponse,
  transformPreviewBroadcastResponse,
  transformGetRecipientOptionsResponse,
  transformValidateRecipientsResponse,
  extractBroadcastErrorMessage,
  formatRecipientCount,
  formatEstimatedDeliveryTime,
} from '../broadcast-response-transformer';

describe('Broadcast Response Transformer', () => {
  describe('transformSendBroadcastResponse', () => {
    it('should transform valid send broadcast response', () => {
      const apiResponse = {
        success: true,
        message: 'Broadcast sent successfully',
        data: {
          notificationId: 'broadcast-123',
          recipientCount: 150,
          estimatedDeliveryTime: 30,
        },
      };

      const result = transformSendBroadcastResponse(apiResponse);

      expect(result).toEqual({
        success: true,
        message: 'Broadcast sent successfully',
        data: {
          notificationId: 'broadcast-123',
          recipientCount: 150,
          scheduledAt: undefined,
          estimatedDeliveryTime: 30,
        },
      });
    });

    it('should handle response with scheduledAt field', () => {
      const apiResponse = {
        success: true,
        message: 'Broadcast sent successfully',
        data: {
          notificationId: 'broadcast-123',
          recipientCount: 150,
          scheduledAt: '2024-12-31T10:00:00Z',
          estimatedDeliveryTime: 30,
        },
      };

      const result = transformSendBroadcastResponse(apiResponse);

      expect(result.data.scheduledAt).toBe('2024-12-31T10:00:00Z');
    });

    it('should throw error for invalid response structure', () => {
      const invalidResponse = {
        success: true,
        // Missing required fields
      };

      expect(() => transformSendBroadcastResponse(invalidResponse)).toThrow();
    });
  });

  describe('transformScheduleBroadcastResponse', () => {
    it('should transform valid schedule broadcast response', () => {
      const apiResponse = {
        success: true,
        message: 'Broadcast scheduled successfully',
        data: {
          notificationId: 'scheduled-123',
          recipientCount: 75,
          scheduledAt: '2024-12-31T10:00:00Z',
          estimatedDeliveryTime: 60,
        },
      };

      const result = transformScheduleBroadcastResponse(apiResponse);

      expect(result).toEqual({
        success: true,
        message: 'Broadcast scheduled successfully',
        data: {
          notificationId: 'scheduled-123',
          recipientCount: 75,
          scheduledAt: '2024-12-31T10:00:00Z',
          estimatedDeliveryTime: 60,
        },
      });
    });

    it('should require scheduledAt field for scheduled broadcasts', () => {
      const invalidResponse = {
        success: true,
        message: 'Broadcast scheduled successfully',
        data: {
          notificationId: 'scheduled-123',
          recipientCount: 75,
          // Missing scheduledAt
          estimatedDeliveryTime: 60,
        },
      };

      expect(() => transformScheduleBroadcastResponse(invalidResponse)).toThrow();
    });
  });

  describe('transformPreviewBroadcastResponse', () => {
    it('should transform valid preview broadcast response', () => {
      const apiResponse = {
        success: true,
        message: 'Preview generated successfully',
        data: {
          renderedContent: 'Hello John, your package is ready!',
          recipientCount: 25,
          estimatedDeliveryTime: 15,
          warnings: ['Some users may have disabled notifications'],
        },
      };

      const result = transformPreviewBroadcastResponse(apiResponse);

      expect(result).toEqual({
        success: true,
        message: 'Preview generated successfully',
        data: {
          renderedContent: 'Hello John, your package is ready!',
          recipientCount: 25,
          estimatedDeliveryTime: 15,
          warnings: ['Some users may have disabled notifications'],
        },
      });
    });

    it('should handle preview response without warnings', () => {
      const apiResponse = {
        success: true,
        message: 'Preview generated successfully',
        data: {
          renderedContent: 'Simple message',
          recipientCount: 10,
          estimatedDeliveryTime: 5,
        },
      };

      const result = transformPreviewBroadcastResponse(apiResponse);

      expect(result.data.warnings).toBeUndefined();
    });
  });

  describe('transformGetRecipientOptionsResponse', () => {
    it('should transform valid recipient options response', () => {
      const apiResponse = {
        success: true,
        message: 'Recipients retrieved successfully',
        data: {
          users: [
            {
              id: 'user-1',
              name: 'John Doe',
              email: '<EMAIL>',
              type: 'customer',
            },
            {
              id: 'user-2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              type: 'operator',
            },
          ],
          roles: [
            {
              id: 'role-1',
              name: 'Customer',
              userCount: 100,
            },
            {
              id: 'role-2',
              name: 'Operator',
              userCount: 25,
            },
          ],
        },
      };

      const result = transformGetRecipientOptionsResponse(apiResponse);

      expect(result).toEqual({
        success: true,
        message: 'Recipients retrieved successfully',
        data: {
          users: [
            {
              id: 'user-1',
              name: 'John Doe',
              email: '<EMAIL>',
              type: 'customer',
            },
            {
              id: 'user-2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              type: 'operator',
            },
          ],
          roles: [
            {
              id: 'role-1',
              name: 'Customer',
              userCount: 100,
            },
            {
              id: 'role-2',
              name: 'Operator',
              userCount: 25,
            },
          ],
        },
      });
    });

    it('should handle empty users and roles arrays', () => {
      const apiResponse = {
        success: true,
        message: 'No recipients found',
        data: {
          users: [],
          roles: [],
        },
      };

      const result = transformGetRecipientOptionsResponse(apiResponse);

      expect(result.data.users).toEqual([]);
      expect(result.data.roles).toEqual([]);
    });
  });

  describe('transformValidateRecipientsResponse', () => {
    it('should transform valid validate recipients response', () => {
      const apiResponse = {
        success: true,
        message: 'Recipients validated successfully',
        data: {
          isValid: true,
          recipientCount: 125,
          warnings: ['Some users may have disabled notifications'],
          errors: [],
        },
      };

      const result = transformValidateRecipientsResponse(apiResponse);

      expect(result).toEqual({
        success: true,
        message: 'Recipients validated successfully',
        data: {
          isValid: true,
          recipientCount: 125,
          warnings: ['Some users may have disabled notifications'],
          errors: [],
        },
      });
    });

    it('should handle validation response with errors', () => {
      const apiResponse = {
        success: false,
        message: 'Validation failed',
        data: {
          isValid: false,
          recipientCount: 0,
          warnings: [],
          errors: ['Invalid role specified', 'User not found'],
        },
      };

      const result = transformValidateRecipientsResponse(apiResponse);

      expect(result.data.isValid).toBe(false);
      expect(result.data.errors).toEqual(['Invalid role specified', 'User not found']);
    });

    it('should provide default empty arrays for warnings and errors', () => {
      const apiResponse = {
        success: true,
        message: 'Recipients validated successfully',
        data: {
          isValid: true,
          recipientCount: 50,
        },
      };

      const result = transformValidateRecipientsResponse(apiResponse);

      expect(result.data.warnings).toEqual([]);
      expect(result.data.errors).toEqual([]);
    });
  });

  describe('extractBroadcastErrorMessage', () => {
    it('should extract error message from validation errors array', () => {
      const error = {
        errors: ['Title is required', 'Content is too long'],
      };

      const result = extractBroadcastErrorMessage(error);

      expect(result).toBe('Title is required, Content is too long');
    });

    it('should extract error message from message field', () => {
      const error = {
        message: 'Invalid recipient selection',
      };

      const result = extractBroadcastErrorMessage(error);

      expect(result).toBe('Invalid recipient selection');
    });

    it('should extract error message from nested data structure', () => {
      const error = {
        data: {
          message: 'Broadcast quota exceeded',
        },
      };

      const result = extractBroadcastErrorMessage(error);

      expect(result).toBe('Broadcast quota exceeded');
    });

    it('should return default message for unknown error structure', () => {
      const error = {
        unknownField: 'some value',
      };

      const result = extractBroadcastErrorMessage(error);

      expect(result).toBe('An unexpected error occurred while processing the broadcast request');
    });

    it('should handle non-object errors', () => {
      const result1 = extractBroadcastErrorMessage('string error');
      const result2 = extractBroadcastErrorMessage(null);
      const result3 = extractBroadcastErrorMessage(undefined);

      expect(result1).toBe('An unexpected error occurred while processing the broadcast request');
      expect(result2).toBe('An unexpected error occurred while processing the broadcast request');
      expect(result3).toBe('An unexpected error occurred while processing the broadcast request');
    });
  });

  describe('formatRecipientCount', () => {
    it('should format zero recipients', () => {
      expect(formatRecipientCount(0)).toBe('No recipients');
    });

    it('should format single recipient', () => {
      expect(formatRecipientCount(1)).toBe('1 recipient');
    });

    it('should format multiple recipients', () => {
      expect(formatRecipientCount(150)).toBe('150 recipients');
    });

    it('should format large numbers with locale formatting', () => {
      expect(formatRecipientCount(1500)).toBe('1,500 recipients');
    });
  });

  describe('formatEstimatedDeliveryTime', () => {
    it('should format seconds', () => {
      expect(formatEstimatedDeliveryTime(30)).toBe('30 seconds');
      expect(formatEstimatedDeliveryTime(45)).toBe('45 seconds');
    });

    it('should format minutes', () => {
      expect(formatEstimatedDeliveryTime(60)).toBe('1 minutes');
      expect(formatEstimatedDeliveryTime(150)).toBe('3 minutes');
      expect(formatEstimatedDeliveryTime(3540)).toBe('59 minutes');
    });

    it('should format hours', () => {
      expect(formatEstimatedDeliveryTime(3600)).toBe('1 hours');
      expect(formatEstimatedDeliveryTime(7200)).toBe('2 hours');
      expect(formatEstimatedDeliveryTime(10800)).toBe('3 hours');
    });
  });
});