import { AdminRole, AdminUser } from '../types/admin.types';

// Define permissions for different admin roles
export enum Permission {
  // Dashboard permissions
  VIEW_DASHBOARD = 'VIEW_DASHBOARD',

  // User management permissions
  VIEW_USERS = 'VIEW_USERS',
  MANAGE_USERS = 'MANAGE_USERS',
  APPROVE_OPERATORS = 'APPROVE_OPERATORS',

  // Admin management permissions (SUPER_ADMIN only)
  VIEW_ADMINS = 'VIEW_ADMINS',
  CREATE_ADMINS = 'CREATE_ADMINS',
  EDIT_ADMINS = 'EDIT_ADMINS',
  DELETE_ADMINS = 'DELETE_ADMINS',
  CHAN<PERSON>_ADMIN_STATUS = 'CHANGE_ADMIN_STATUS',

  // Profile permissions
  VIEW_PROFILE = 'VIEW_PROFILE',
  EDIT_PROFILE = 'EDIT_PROFILE',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',

  // System permissions
  VIEW_SYSTEM_SETTINGS = 'VIEW_SYSTEM_SETTINGS',
  MANAGE_SYSTEM_SETTINGS = 'MANAGE_SYSTEM_SETTINGS',

  // Reports permissions
  VIEW_REPORTS = 'VIEW_REPORTS',
  EXPORT_REPORTS = 'EXPORT_REPORTS',

  // Notification management permissions
  VIEW_NOTIFICATIONS = 'VIEW_NOTIFICATIONS',
  MANAGE_NOTIFICATIONS = 'MANAGE_NOTIFICATIONS',
  SEND_BROADCAST_MESSAGES = 'SEND_BROADCAST_MESSAGES',
  MANAGE_NOTIFICATION_TEMPLATES = 'MANAGE_NOTIFICATION_TEMPLATES',
  VIEW_NOTIFICATION_ANALYTICS = 'VIEW_NOTIFICATION_ANALYTICS',
}

// Base permissions for ADMIN role
const ADMIN_PERMISSIONS: Permission[] = [
  // Dashboard
  Permission.VIEW_DASHBOARD,

  // User management
  Permission.VIEW_USERS,
  Permission.MANAGE_USERS,
  Permission.APPROVE_OPERATORS,

  // Profile
  Permission.VIEW_PROFILE,
  Permission.EDIT_PROFILE,
  Permission.CHANGE_PASSWORD,

  // Reports
  Permission.VIEW_REPORTS,
  Permission.EXPORT_REPORTS,

  // Notifications
  Permission.VIEW_NOTIFICATIONS,
  Permission.MANAGE_NOTIFICATIONS,
  Permission.SEND_BROADCAST_MESSAGES,
  Permission.MANAGE_NOTIFICATION_TEMPLATES,
  Permission.VIEW_NOTIFICATION_ANALYTICS,
];

// Additional permissions for SUPER_ADMIN role
const SUPER_ADMIN_ADDITIONAL_PERMISSIONS: Permission[] = [
  Permission.VIEW_ADMINS,
  Permission.CREATE_ADMINS,
  Permission.EDIT_ADMINS,
  Permission.DELETE_ADMINS,
  Permission.CHANGE_ADMIN_STATUS,

  // System management
  Permission.VIEW_SYSTEM_SETTINGS,
  Permission.MANAGE_SYSTEM_SETTINGS,
];

// Role-based permission mapping
const ROLE_PERMISSIONS: Record<AdminRole, Permission[]> = {
  [AdminRole.ADMIN]: ADMIN_PERMISSIONS,
  [AdminRole.SUPER_ADMIN]: [...ADMIN_PERMISSIONS, ...SUPER_ADMIN_ADDITIONAL_PERMISSIONS],
};

/**
 * Check if a user has a specific permission
 */
export const hasPermission = (user: AdminUser | null, permission: Permission): boolean => {
  if (!user) return false;

  const userPermissions = ROLE_PERMISSIONS[user.role] || [];
  return userPermissions.includes(permission);
};

/**
 * Check if a user has any of the specified permissions
 */
export const hasAnyPermission = (user: AdminUser | null, permissions: Permission[]): boolean => {
  if (!user) return false;

  return permissions.some((permission) => hasPermission(user, permission));
};

/**
 * Check if a user has all of the specified permissions
 */
export const hasAllPermissions = (user: AdminUser | null, permissions: Permission[]): boolean => {
  if (!user) return false;

  return permissions.every((permission) => hasPermission(user, permission));
};

/**
 * Get all permissions for a user's role
 */
export const getUserPermissions = (user: AdminUser | null): Permission[] => {
  if (!user) return [];

  return ROLE_PERMISSIONS[user.role] || [];
};

/**
 * Check if a user can manage another admin
 */
export const canManageAdmin = (currentUser: AdminUser | null, targetAdmin: AdminUser): boolean => {
  if (!currentUser) return false;

  // Only SUPER_ADMIN can manage other admins
  if (currentUser.role !== AdminRole.SUPER_ADMIN) return false;

  // Cannot manage yourself
  return currentUser.id !== targetAdmin.id;
};

/**
 * Check if a user can access admin management features
 */
export const canAccessAdminManagement = (user: AdminUser | null): boolean => hasPermission(user, Permission.VIEW_ADMINS);

/**
 * Check if a user can create new admins
 */
export const canCreateAdmin = (user: AdminUser | null): boolean => hasPermission(user, Permission.CREATE_ADMINS);

/**
 * Check if a user can edit admin information
 */
export const canEditAdmin = (user: AdminUser | null): boolean => hasPermission(user, Permission.EDIT_ADMINS);

/**
 * Check if a user can delete admins
 */
export const canDeleteAdmin = (user: AdminUser | null): boolean => hasPermission(user, Permission.DELETE_ADMINS);

/**
 * Check if a user can change admin status
 */
export const canChangeAdminStatus = (user: AdminUser | null): boolean => hasPermission(user, Permission.CHANGE_ADMIN_STATUS);

/**
 * Check if a user can manage users
 */
export const canManageUsers = (user: AdminUser | null): boolean => hasPermission(user, Permission.MANAGE_USERS);

/**
 * Check if a user can approve operators
 */
export const canApproveOperators = (user: AdminUser | null): boolean => hasPermission(user, Permission.APPROVE_OPERATORS);

/**
 * Check if a user can access system settings
 */
export const canAccessSystemSettings = (user: AdminUser | null): boolean => hasPermission(user, Permission.VIEW_SYSTEM_SETTINGS);

/**
 * Check if a user can manage system settings
 */
export const canManageSystemSettings = (user: AdminUser | null): boolean => hasPermission(user, Permission.MANAGE_SYSTEM_SETTINGS);

/**
 * Check if a user can view notifications
 */
export const canViewNotifications = (user: AdminUser | null): boolean => hasPermission(user, Permission.VIEW_NOTIFICATIONS);

/**
 * Check if a user can manage notifications
 */
export const canManageNotifications = (user: AdminUser | null): boolean => hasPermission(user, Permission.MANAGE_NOTIFICATIONS);

/**
 * Check if a user can send broadcast messages
 */
export const canSendBroadcastMessages = (user: AdminUser | null): boolean => hasPermission(user, Permission.SEND_BROADCAST_MESSAGES);

/**
 * Check if a user can manage notification templates
 */
export const canManageNotificationTemplates = (user: AdminUser | null): boolean => hasPermission(user, Permission.MANAGE_NOTIFICATION_TEMPLATES);

/**
 * Check if a user can view notification analytics
 */
export const canViewNotificationAnalytics = (user: AdminUser | null): boolean => hasPermission(user, Permission.VIEW_NOTIFICATION_ANALYTICS);

/**
 * Get user role display name
 */
export const getRoleDisplayName = (role: AdminRole): string => {
  switch (role) {
    case AdminRole.SUPER_ADMIN:
      return 'Super Administrator';
    case AdminRole.ADMIN:
      return 'Administrator';
    default:
      return 'Unknown';
  }
};

/**
 * Get user role description
 */
export const getRoleDescription = (role: AdminRole): string => {
  switch (role) {
    case AdminRole.SUPER_ADMIN:
      return 'Full system access including admin management and system settings';
    case AdminRole.ADMIN:
      return 'User management and dashboard access';
    default:
      return 'No permissions';
  }
};
