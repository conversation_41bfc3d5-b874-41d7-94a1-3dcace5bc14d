import { z } from 'zod';
import { customFieldsTags, paymentMethodTag } from './response-transformer';

const customFieldsFrontendSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().nullable(),
    nameAr: z.string().nullable(),
    type: customFieldsTags.or(z.string()).nullable(),
    placeholder: z.string().nullable().optional(),
    placeholderAr: z.string().nullable().optional(),
    regex: z.string().nullable(),
    listItems: z.array(z.string().nullable().optional()).optional(),
    slug: z.string().nullable().optional(),
  });

// update payment method data request schema in front end
export const updatePaymentMethodApiRequestSchema = z.object({
  label: z.string(),
  labelAr: z.string().nullable(),
  tag: paymentMethodTag,
  depositFeesFixed: z.number().or(z.string()),
  depositFeesPercentage: z.number().or(z.string()),
  withdrawFeesFixed: z.number().or(z.string()),
  withdrawFeesPercentage: z.number().or(z.string()),
  depositAmountMin: z.number().or(z.string()),
  depositAmountMax: z.number().or(z.string()).nullable(),
  withdrawAmountMin: z.number().or(z.string()),
  withdrawAmountMax: z.number().or(z.string()).nullable(),
  depositFeesMin: z.number().or(z.string()),
  depositFeesMax: z.number().or(z.string()).nullable(),
  withdrawFeesMin: z.number().or(z.string()),
  withdrawFeesMax: z.number().or(z.string()).nullable(),
  showInRatesBar: z.boolean().nullable().optional(),
  depositAddress: z.string().nullable().optional(),
  shortDescriptionDeposit: z.string().nullable().optional(),
  shortDescriptionDepositAr: z.string().nullable().optional(),
  descriptionDeposit: z.string().nullable().optional(),
  descriptionDepositAr: z.string().nullable().optional(),
  shortDescriptionWithdraw: z.string().nullable().optional(),
  shortDescriptionWithdrawAr: z.string().nullable().optional(),
  descriptionWithdraw: z.string().nullable().optional(),
  descriptionWithdrawAr: z.string().nullable().optional(),
  icon: z
    .object({
      id: z.number().or(z.string()).nullable().optional(),
      url: z.string().nullable().optional(),
    })
    .nullable(),
  depositCustomFields: z.array(customFieldsFrontendSchema).nullable().optional(),
  withdrawCustomFields: z.array(customFieldsFrontendSchema).nullable().optional(),
  publishedAt: z.string().nullable(),
  depositCurrencies: z.array(z.string()),
  withdrawCurrencies: z.array(z.string()),
  badges: z.array(z.string()).nullable().optional(),
  order: z.number().min(1, { message: 'Order must be at least 1 ' }).nullable().optional(),
});
// update payment method data request schema in back end
export const updatePaymentMethodBackendRequestSchema = updatePaymentMethodApiRequestSchema.transform((data) => ({
  data: {
    title: data?.label,
    title_ar: data?.labelAr,
    tag: data?.tag,
    deposit_fees_fixed: `${data?.depositFeesFixed}`,
    deposit_fees_percentage: `${data?.depositFeesPercentage}`,
    withdraw_fees_fixed: `${data?.withdrawFeesFixed}`,
    withdraw_fees_percentage: `${data?.withdrawFeesPercentage}`,
    deposit_amount_min: `${data?.depositAmountMin}`,
    deposit_amount_max: data?.depositAmountMax
      ? `${data?.depositAmountMax}`
      : null,
    withdraw_amount_min: `${data?.withdrawAmountMin}`,
    withdraw_amount_max: data?.withdrawAmountMax
      ? `${data?.withdrawAmountMax}`
      : null,
    deposit_fees_min: `${data?.depositFeesMin}`,
    deposit_fees_max: data?.depositFeesMax ? `${data?.depositFeesMax}` : null,
    withdraw_fees_min: `${data?.withdrawFeesMin}`,
    withdraw_fees_max: data?.withdrawFeesMax
      ? `${data?.withdrawFeesMax}`
      : null,
    show_in_rates_bar: data?.showInRatesBar,
    deposit_address: data?.depositAddress,
    icon: data?.icon?.id,
    deposit_custom_fields: data?.depositCustomFields?.map((field) => (field ? {
      name: field.name,
      name_ar: field.nameAr,
      type: field.type,
      placeholder: field.placeholder,
      placeholder_ar: field.placeholderAr,
      regex: field.regex,
      list_items: field.listItems,
      slug: field.slug,
    } : null)) ?? [],
    withdraw_custom_fields: data?.withdrawCustomFields?.map((field) => (field ? {
      name: field.name,
      name_ar: field.nameAr,
      type: field.type,
      placeholder: field.placeholder,
      placeholder_ar: field.placeholderAr,
      regex: field.regex,
      list_items: field.listItems,
      slug: field.slug,
    } : null)) ?? [],

    short_description_withdraw: data?.shortDescriptionWithdraw,
    short_description_withdraw_ar: data?.shortDescriptionWithdrawAr,

    description_withdraw: data?.descriptionWithdraw,
    description_withdraw_ar: data?.descriptionWithdrawAr,

    short_description_deposit: data?.shortDescriptionDeposit,
    short_description_deposit_ar: data?.shortDescriptionDepositAr,

    description_deposit: data?.descriptionDeposit,
    description_deposit_ar: data?.descriptionDepositAr,

    publishedAt: data?.publishedAt,
    withdraw_currencies: data?.withdrawCurrencies ?? [],
    deposit_currencies: data?.depositCurrencies ?? [],
    badges: data.badges?.map((i) => +i),
    order: data.order,
  },
}));
