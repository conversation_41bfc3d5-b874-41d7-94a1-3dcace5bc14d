/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Container, Title, Text, Grid, Paper, Group, Button, LoadingOverlay, Alert,
} from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useAdminAuthContext } from '../../src/contexts/AdminAuthContext';
import { getDashboardStatsQuery } from '../../src/requests/admin-dashboard';
import { StatsGrid, RecentActivities, QuickActions } from '../../src/components/dashboard';

export default function AdminDashboard() {
  const { admin, logout } = useAdminAuthContext();

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useQuery(getDashboardStatsQuery);

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <LoadingOverlay visible />
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading dashboard"
          color="red"
          variant="light"
        >
          <Text mb="md">
            Failed to load dashboard data. Please try again.
          </Text>
          <Group>
            <Button variant="outline" onClick={() => refetch()}>
              Retry
            </Button>
            <Button color="red" onClick={logout}>
              Logout
            </Button>
          </Group>
        </Alert>
      </Container>
    );
  }

  const stats = dashboardData?.data;

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>
            Admin Dashboard
          </Title>
          <Text c="dimmed" size="lg">
            Welcome back,
            {' '}
            {admin?.name}
            !
          </Text>
        </div>
        <Button color="red" variant="outline" onClick={logout}>
          Logout
        </Button>
      </Group>

      {stats ? (
        <>
          {/* Statistics Grid */}
          <StatsGrid
            userStats={{
              ...stats.dashboard_data.user_stats,
              active_users: stats.dashboard_data.user_stats.active_users ?? 0,
              inactive_users: stats.dashboard_data.user_stats.inactive_users ?? 0,
            }}
            shipmentStats={{
              ...stats.dashboard_data.shipment_stats,
              cancelled: stats.dashboard_data.shipment_stats.cancelled ?? 0,
              expired: stats.dashboard_data.shipment_stats.expired ?? 0,
              pending: stats.dashboard_data.shipment_stats.pending ?? 0,
              in_transit: stats.dashboard_data.shipment_stats.in_transit ?? 0,
            }}
          />

          {/* Recent Activities and Quick Actions */}
          <Grid mt="lg">
            <Grid.Col span={{ base: 12, md: 8 }}>
              <RecentActivities
                activities={stats.dashboard_data.recent_users.map((user: any) => ({
                  id: user.id,
                  type: 'user_registration',
                  description: `New ${user.user_type.toLowerCase().replace('_', ' ')} registered: ${user.name}`,
                  timestamp: user.created_at,
                  user_name: user.name,
                }))}
              />
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <QuickActions
                actions={stats.dashboard_data.quick_actions.map((action: any) => ({
                  title: action.label,
                  description: `Click to ${action.action.replace('_', ' ')}`,
                  action: action.action,
                  count: 0,
                }))}
              />
            </Grid.Col>
          </Grid>
        </>
      ) : (
        <Paper withBorder shadow="sm" p="lg">
          <Text ta="center" c="dimmed">
            No dashboard data available
          </Text>
        </Paper>
      )}
    </Container>
  );
}
