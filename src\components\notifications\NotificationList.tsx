/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable camelcase */
import React, { useState } from 'react';
import {
  Box,
  Stack,
  Group,
  Button,
  Select,
  TextInput,
  Pagination,
  Text,
  Center,
  Loader,
  Paper,
  Badge,
  ActionIcon,
  Divider,
} from '@mantine/core';
import {
  IconSearch,
  IconCheck,
  IconBellRinging,
  IconRefresh,
} from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { useDebouncedValue } from '@mantine/hooks';
import {
  NotificationPriority,
  NotificationType,
} from '../../requests/notifications';
import NotificationItem from './NotificationItem';
import {
  getNotificationsQuery,
  markAllAsReadMutationWithInvalidation,
  markAsReadMutationWithInvalidation,
} from '../../requests/notifications/calls';

interface NotificationListProps {
  pageSize?: number;
  showFilters?: boolean;
  showPagination?: boolean;
  onNotificationClick?: (notification: any) => void;
}

export default function NotificationList({
  pageSize = 20,
  showFilters = true,
  showPagination = true,
  onNotificationClick,
}: NotificationListProps) {
  const [currentPage, setCurrentPage] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [readFilter, setReadFilter] = useState<string | null>(null);
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  const [priorityFilter, setPriorityFilter] = useState<string | null>(null);

  const [debouncedSearchTerm] = useDebouncedValue(searchTerm, 300);
  const queryClient = useQueryClient();

  // Optional: Fetch dynamic filter options from server
  // const { data: filterOptions } = useQuery(
  //   getFilterOptionsQuery({ enabled: true })
  // );

  // Build query parameters
  const queryParams = {
    page: currentPage,
    limit: pageSize,
    ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
    ...(readFilter !== null && { read: readFilter === 'true' }),
    ...(typeFilter && { type: typeFilter }),
    ...(priorityFilter && { priority: priorityFilter }),
  };

  // Fetch notifications
  const {
    data: notificationData,
    isLoading,
    refetch,
    isFetching,
  } = useQuery(
    getNotificationsQuery({
      params: queryParams,
      enabled: true,
    }),
  );

  const notificationsList = notificationData?.notifications || [];
  const totalCount = notificationData?.pagination?.total || 0;
  const unreadCount = notificationData?.unreadCount || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Mutations
  const markAsReadMutation = useMutation(
    markAsReadMutationWithInvalidation(queryClient),
  );

  const markAllAsReadMutation = useMutation(
    markAllAsReadMutationWithInvalidation(queryClient),
  );

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsReadMutation.mutateAsync({ notificationId });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to mark as read',
        color: 'red',
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    try {
      await markAllAsReadMutation.mutateAsync({});
      notifications.show({
        title: 'Success',
        message: 'All notifications marked as read',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to mark as read',
        color: 'red',
      });
    }
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setReadFilter(null);
    setTypeFilter(null);
    setPriorityFilter(null);
    setCurrentPage(0);
  };

  const hasActiveFilters = searchTerm || readFilter || typeFilter || priorityFilter;

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between" align="center">
        <Group gap="sm">
          <Text size="lg" fw={600}>
            Notifications
          </Text>
          {unreadCount > 0 && (
            <Badge color="blue" variant="light">
              {unreadCount}
              {' '}
              unread
            </Badge>
          )}
        </Group>

        <Group gap="sm">
          <ActionIcon
            variant="subtle"
            onClick={() => refetch()}
            loading={isFetching}
            title="Refresh notifications"
          >
            <IconRefresh size="1.1rem" />
          </ActionIcon>

          {unreadCount > 0 && (
            <Button
              variant="light"
              size="sm"
              leftSection={<IconCheck size="1rem" />}
              onClick={handleMarkAllAsRead}
              loading={markAllAsReadMutation.isPending}
            >
              Mark All as Read
            </Button>
          )}
        </Group>
      </Group>

      {/* Filters */}
      {showFilters && (
        <Paper p="md" withBorder>
          <Stack gap="md">
            <Group gap="md" align="end">
              <TextInput
                placeholder="Search notifications"
                leftSection={<IconSearch size="1rem" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ flex: 1 }}
              />

              <Select
                placeholder="Status"
                data={[
                  { value: 'false', label: 'Unread' },
                  { value: 'true', label: 'Read' },
                ]}
                value={readFilter}
                onChange={setReadFilter}
                clearable
                w={120}
              />

              <Select
                placeholder="Priority"
                data={Object.values(NotificationPriority).map((priority) => ({
                  value: priority,
                  label: priority.charAt(0) + priority.slice(1).toLowerCase(),
                }))}
                value={priorityFilter}
                onChange={setPriorityFilter}
                clearable
                w={120}
              />

              <Select
                placeholder="Type"
                data={Object.values(NotificationType).map((type) => ({
                  value: type,
                  label: type.charAt(0) + type.slice(1).toLowerCase(),
                }))}
                value={typeFilter}
                onChange={setTypeFilter}
                clearable
                w={200}
              />
            </Group>

            {hasActiveFilters && (
              <Group justify="space-between">
                <Text size="sm" c="dimmed">
                  {totalCount}
                  {' '}
                  {totalCount === 1 ? 'notification' : 'notifications'}
                  {' '}
                  found
                </Text>
                <Button
                  variant="subtle"
                  size="xs"
                  onClick={handleClearFilters}
                >
                  Clear Filters
                </Button>
              </Group>
            )}
          </Stack>
        </Paper>
      )}

      {/* Content */}
      <Box>
        {isLoading ? (
          <Center py="xl">
            <Stack align="center" gap="sm">
              <Loader />
              <Text size="sm" c="dimmed">
                Loading notifications...
              </Text>
            </Stack>
          </Center>
        ) : notificationsList.length === 0 ? (
          <Center py="xl">
            <Stack align="center" gap="sm">
              <IconBellRinging size="3rem" color="var(--mantine-color-gray-5)" />
              <Text size="lg" c="dimmed" ta="center">
                {hasActiveFilters ? 'No notifications match your filters' : 'No notifications yet'}
              </Text>
              {hasActiveFilters && (
                <Button variant="light" onClick={handleClearFilters}>
                  Clear Filters
                </Button>
              )}
            </Stack>
          </Center>
        ) : (
          <Paper withBorder>
            <Stack gap={0}>
              {notificationsList.map((notification, index) => (
                <Box key={notification.id} style={{}}>
                  <NotificationItem
                    notification={notification}
                    onMarkAsRead={handleMarkAsRead}
                    // onClick={() => onNotificationClick?.(notification)}
                  />
                  {index < notificationsList.length - 1 && (
                    <Divider />
                  )}
                </Box>
              ))}
            </Stack>
          </Paper>
        )}
      </Box>

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <Group justify="center">
          <Pagination
            total={totalPages}
            value={currentPage + 1}
            onChange={(page) => setCurrentPage(page - 1)}
            size="sm"
          />
        </Group>
      )}
    </Stack>
  );
}
