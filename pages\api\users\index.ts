/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable complexity */
/**
 * Handler for user list operations
 * Handles GET /api/users for user list and bulk operations
 */
import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data/api-endpoints';
import { handleBackendError } from '../../../src/utils/handle-backend-error';
import {
  returnUserListParams,
  transformUserListResponse,
  transformBulkOperationRequest,
  transformBulkOperationResponse,
} from '../../../src/requests/admin-users';
import { getJwt } from '../../../src/utils';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (!token) {
    return res.status(401).json({
      success: false,
      error: {
        type: 'AUTHENTICATION_ERROR',
        message: 'No token provided',
      },
    });
  }

  // Get users list
  if (req.method === 'GET') {
    try {
      const params = returnUserListParams(req);
      const response = await BACKEND_API.get(API_ENDPOINT.users.list, {
        params,
        headers: { Authorization: token },
      });

      const transformedData = transformUserListResponse(response.data);
      return res.status(200).json(transformedData);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to fetch users';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Bulk operations
  if (req.method === 'POST' && (req.body.operation === 'bulk-approve' || req.body.operation === 'bulk-reject')) {
    try {
      const transformedData = transformBulkOperationRequest(req.body);
      const endpoint = req.body.operation === 'bulk-approve'
        ? API_ENDPOINT.users.bulkApprove
        : API_ENDPOINT.users.bulkReject;

      const response = await BACKEND_API.post(endpoint, transformedData, {
        headers: { Authorization: token },
      });

      const transformedResponse = transformBulkOperationResponse(response.data);
      return res.status(200).json(transformedResponse);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to perform bulk operation';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Method not allowed
  return res.status(405).json({
    success: false,
    error: {
      message: 'Method not allowed',
      code: 405,
    },
  });
}

export default handler;
