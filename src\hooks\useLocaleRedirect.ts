import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { getCookie, setCookie } from 'cookies-next';
import { LocaleCookie, defaultLocale } from '../data';

export function useLocaleRedirect() {
  const router = useRouter();
  const hasRedirected = useRef(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Only run on client side and only once
    if (typeof window === 'undefined' || hasRedirected.current) return;

    const currentLocale = router.locale || defaultLocale;

    // Since we only support English now, ensure we're always on English
    if (currentLocale !== 'en') {
      hasRedirected.current = true;

      // Use replace to avoid adding to history
      router.replace(router.asPath, router.asPath, {
        locale: 'en',
      });
      return;
    }

    // Save current locale to cookie with long expiration
    setCookie(LocaleCookie, 'en', {
      maxAge: 365 * 24 * 60 * 60, // 1 year
      sameSite: 'lax',
      path: '/',
    });

    // Mark as initialized after a short delay to prevent flash
    setTimeout(() => setIsInitialized(true), 100);
  }, [router.locale, router.asPath, router]);

  return {
    currentLocale: router.locale || defaultLocale,
    isRedirecting: hasRedirected.current,
    isInitialized,
  };
}
