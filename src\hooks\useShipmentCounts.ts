import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { getMyShipmentsQuery, getMyTransportedShipmentsQuery, getPendingShipmentsQuery } from '../requests/shipment/index.js';
import { ACCESS_OPERATOR_USER_TYPE, CAR_OPERATOR_USER_TYPE } from '../data';

/**
 * Hook to get various shipment counts based on user type
 * Returns counts for badges display in sidebar
 */
export const useShipmentCounts = () => {
  const { data: session, status } = useSession();
  const userType = session?.user?.user_type;
  const isAuthenticated = status === 'authenticated';

  // For Access Operators - Pending shipments
  const {
    data: pendingShipmentsData,
    isLoading: isPendingLoading,
  } = useQuery({
    ...getPendingShipmentsQuery({
      pagination: {
        page: 0,
        pageSize: 1, // We only need the count
      },
      filters: {
        status: 'PENDING',
        originAoId: session?.user?.id || undefined, // Only show shipments where this AO is the origin
        role: 'origin',
      },
    }),
    enabled: isAuthenticated && userType === ACCESS_OPERATOR_USER_TYPE && !!session?.user?.id,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // For Access Operators - Incoming (In-Transit destined to this AO) shipments
  const {
    data: incomingShipmentsData,
    isLoading: isIncomingLoading,
  } = useQuery({
    ...getMyShipmentsQuery({
      pagination: {
        page: 0,
        pageSize: 1,
      },
      filters: {
        status: 'IN_TRANSIT',
        destAoId: session?.user?.id || undefined,
      },
    }),
    enabled: isAuthenticated && userType === ACCESS_OPERATOR_USER_TYPE && !!session?.user?.id,
    refetchInterval: 30000,
  });

  // For Car Operators - Available (awaiting pickup) shipments
  const {
    data: availableShipmentsData,
    isLoading: isAvailableLoading,
  } = useQuery({
    ...getMyTransportedShipmentsQuery({
      pagination: {
        page: 0,
        pageSize: 1,
      },
      filters: {
        status: 'AWAITING_PICKUP',
      },
    }),
    enabled: isAuthenticated && userType === CAR_OPERATOR_USER_TYPE,
    refetchInterval: 30000,
  });

  // For Car Operators - In Transit shipments
  const {
    data: inTransitShipmentsData,
    isLoading: isInTransitLoading,
  } = useQuery({
    ...getMyTransportedShipmentsQuery({
      pagination: {
        page: 0,
        pageSize: 1,
      },
      filters: {
        status: 'IN_TRANSIT',
      },
    }),
    enabled: isAuthenticated && userType === CAR_OPERATOR_USER_TYPE,
    refetchInterval: 30000,
  });

  return {
    // Access Operator counts
    pendingCount: pendingShipmentsData?.data?.pagination?.total || 0,
    incomingCount: incomingShipmentsData?.data?.pagination?.total || 0,
    isPendingLoading,
    isIncomingLoading,

    // Car Operator counts
    availableCount: availableShipmentsData?.data?.pagination?.total || 0,
    inTransitCount: inTransitShipmentsData?.data?.pagination?.total || 0,
    isAvailableLoading,
    isInTransitLoading,

    // Status info
    isEnabled: isAuthenticated && (userType === ACCESS_OPERATOR_USER_TYPE || userType === CAR_OPERATOR_USER_TYPE),
  };
};
