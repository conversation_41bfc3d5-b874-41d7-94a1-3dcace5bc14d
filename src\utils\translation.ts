/* eslint-disable no-return-await */
import { getCookie } from 'cookies-next';
import { defaultLocale, LocaleCookie } from '../data';

/**
 * Get the current locale - always returns 'en' since we only support English
 * Safe for both client and server side usage
 */
export const getCurrentLocale = (): string => {
  // Always return English since we only support English now
  return 'en';
};

/**
 * Get translation function for a specific namespace - Returns a simple function that returns the key
 */
export const getTranslation = async (namespace: string = 'common') => {
  // Since we removed translations, return a simple function that returns the key
  return (key: string, params?: any) => key;
};

/**
 * Get translation function for common namespace
 */
export const getCommonTranslation = async () => await getTranslation('common');

/**
 * Get translation function for error namespace
 */
export const getErrorTranslation = async () => await getTranslation('error');

/**
 * Translate a key with fallback - Always returns fallback since translations are removed
 */
export const translateWithFallback = async (
  key: string,
  fallback: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: Record<string, any>,
  namespace: string = 'common',
): Promise<string> => {
  // Always return fallback since we removed translations
  return fallback;
};

/**
 * Translate error message with fallback
 */
export const translateError = async (
  key: string,
  fallback: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: Record<string, any>,
): Promise<string> => translateWithFallback(key, fallback, params, 'error');

/**
 * Get direction based on current locale - Always LTR
 */
export const getDirection = (): 'ltr' | 'rtl' => 'ltr'; // Always return LTR

/**
 * Check if current locale is RTL - Always false
 */
export const isRTL = (): boolean => false; // Always return false

/**
 * Format date according to locale - English only
 */
export const formatDateForLocale = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Get locale-specific number formatting - English only
 */
export const formatNumberForLocale = (number: number): string => {
  return number.toLocaleString('en-US');
};
