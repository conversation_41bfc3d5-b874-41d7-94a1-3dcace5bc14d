import {
  dashboardResponseSchema,
  expiredShipmentStatsResponseSchema,
  DashboardResponseType,
  ExpiredShipmentStatsResponseType,
} from './types';

// Response transformers - convert backend data to frontend format
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const transformDashboardResponse = (data: any): DashboardResponseType => dashboardResponseSchema.parse(data);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const transformExpiredShipmentStatsResponse = (data: any): ExpiredShipmentStatsResponseType => {
  const validated = expiredShipmentStatsResponseSchema.parse(data);
  return {
    success: validated.success,
    data: {
      expired_shipments: {
        total: validated.data.expired_shipments.total,
        last_24h: validated.data.expired_shipments.last_24h,
        last_week: validated.data.expired_shipments.last_week,
        last_month: validated.data.expired_shipments.last_month,
      },
      expiry_reasons: validated.data.expiry_reasons.map((reason) => ({
        reason: reason.reason,
        count: reason.count,
      })),
    },
  };
};
