import React, { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Text,
  Badge,
  ActionIcon,
  Tooltip,

  Box,
} from '@mantine/core';
import {
  IconRefresh,
  IconBroadcast,
  IconTemplate,
  IconChartBar,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import AdminLayout from '../../src/components/layouts/AdminLayout';
import { PermissionGuard } from '../../src/components/auth/PermissionGuard';
import { Permission } from '../../src/utils/permissions';
import { NotificationList } from '../../src/components/notifications';
import { getNotificationsQuery } from '../../src/requests/notifications/calls';
import { useLoading } from '../../src/contexts/LoadingContext';

export default function NotificationsManagementPage() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch notifications with admin-focused parameters
  const {
    data: notificationData,
    isLoading,
    refetch,
  } = useQuery({
    ...getNotificationsQuery({
      params: {
        page: 0,
        limit: 50,
        // Add admin-specific parameters when API supports them
      },
      enabled: true,
    }),
    queryKey: ['admin-notifications', refreshKey],
  });

  // Use global loading for this query
  useEffect(() => {
    if (isLoading) {
      showLoading('Loading notifications...');
    } else {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading]);

  const handleRefresh = () => {
    setRefreshKey((prev) => prev + 1);
    refetch();
    notifications.show({
      title: 'Refreshed',
      message: 'Notifications refreshed',
      color: 'green',
    });
  };

  const handleCreateBroadcast = () => {
    router.push('/notifications/broadcast');
  };

  const handleManageTemplates = () => {
    router.push('/notifications/templates');
  };

  const handleViewAnalytics = () => {
    router.push('/notifications/analytics');
  };

  // const notificationsList = notificationData?.notifications || [];
  const unreadCount = notificationData?.unreadCount || 0;
  const totalCount = notificationData?.pagination?.total || 0;

  return (
    <AdminLayout>
      <Container size="xl" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <Paper withBorder p="xl">
            <Group justify="space-between" align="flex-start">
              <Box>
                <Title order={1} size="h2" mb="xs">
                  Notification Management
                </Title>
                <Text c="dimmed" size="sm">
                  Manage system notifications and broadcasts
                </Text>
              </Box>

              <Group gap="sm">
                <Tooltip label="Refresh">
                  <ActionIcon
                    variant="light"
                    size="lg"
                    onClick={handleRefresh}
                    loading={false}
                  >
                    <IconRefresh size="1.2rem" />
                  </ActionIcon>
                </Tooltip>

                <PermissionGuard permission={Permission.VIEW_NOTIFICATION_ANALYTICS}>
                  <Button
                    variant="light"
                    leftSection={<IconChartBar size="1rem" />}
                    onClick={handleViewAnalytics}
                  >
                    Analytics
                  </Button>
                </PermissionGuard>

                <PermissionGuard permission={Permission.MANAGE_NOTIFICATION_TEMPLATES}>
                  <Button
                    variant="light"
                    leftSection={<IconTemplate size="1rem" />}
                    onClick={handleManageTemplates}
                  >
                    Templates
                  </Button>
                </PermissionGuard>

                <PermissionGuard permission={Permission.SEND_BROADCAST_MESSAGES}>
                  <Button
                    leftSection={<IconBroadcast size="1rem" />}
                    onClick={handleCreateBroadcast}
                  >
                    Send Broadcast
                  </Button>
                </PermissionGuard>
              </Group>
            </Group>
          </Paper>

          {/* Stats */}
          <Paper withBorder p="xl" shadow="sm">
            <Group gap="xl">
              <div>
                <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                  Total Notifications
                </Text>
                <Text size="xl" fw={700}>
                  {totalCount.toLocaleString()}
                </Text>
              </div>
              <div>
                <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                  Unread Notifications
                </Text>
                <Group gap="xs" align="center">
                  <Text size="xl" fw={700}>
                    {unreadCount.toLocaleString()}
                  </Text>
                  {unreadCount > 0 && (
                    <Badge color="red" size="sm">
                      New
                    </Badge>
                  )}
                </Group>
              </div>
            </Group>
          </Paper>

          {/* Notifications List */}
          <Paper withBorder p="xl" pos="relative" shadow="sm">
            <Title order={3} mb="lg">Notifications List</Title>
            <NotificationList
              pageSize={20}
              showFilters
              showPagination
              onNotificationClick={(notification) => {
                // TODO: Open notification details modal
                console.log('Notification clicked:', notification);
              }}
            />
          </Paper>
        </Stack>
      </Container>
    </AdminLayout>
  );
}

// Protect the page with authentication
NotificationsManagementPage.auth = true;
