import {
  userListParamsSchema,
  userStatusChangeRequestSchema,
  userApprovalRequestSchema,
  approvalRequestSchema,
  comprehensiveUserManagementRequestSchema,
  bulkOperationRequestSchema,
  UserListParamsType,
  UserStatusChangeRequestType,
  UserApprovalRequestType,
  ApprovalRequestType,
  ComprehensiveUserManagementRequestType,
  BulkOperationRequestType,
} from './types';

// Request transformers - convert frontend data to backend format
export const transformUserListParams = (params: any) => {
  // Convert string parameters to appropriate types before validation
  const processedParams: any = { ...params };

  // Convert page and limit from strings to numbers if they exist
  if (processedParams.page !== undefined && processedParams.page !== '') {
    const pageNum = parseInt(processedParams.page, 10);
    processedParams.page = isNaN(pageNum) ? undefined : pageNum;
  }

  if (processedParams.limit !== undefined && processedParams.limit !== '') {
    const limitNum = parseInt(processedParams.limit, 10);
    processedParams.limit = isNaN(limitNum) ? undefined : limitNum;
  }

  const validated = userListParamsSchema.parse(processedParams);

  // Remove undefined values and create clean params object
  const cleanParams: Record<string, unknown> = {};

  if (validated.page !== undefined) cleanParams.page = validated.page;
  if (validated.limit !== undefined) cleanParams.limit = validated.limit;
  if (validated.search !== undefined) cleanParams.search = validated.search;
  if (validated.user_type !== undefined) cleanParams.user_type = validated.user_type;
  if (validated.status !== undefined) cleanParams.status = validated.status;
  if (validated.approval_status !== undefined) cleanParams.approval_status = validated.approval_status;

  return cleanParams;
};

export const transformUserStatusChangeRequest = (data: UserStatusChangeRequestType) => {
  const validated = userStatusChangeRequestSchema.parse(data);
  return {
    userId: validated.userId,
    status: validated.status,
    reason: validated.reason,
  };
};

export const transformUserApprovalRequest = (data: UserApprovalRequestType) => {
  const validated = userApprovalRequestSchema.parse(data);
  return {
    userId: validated.userId,
    approval_status: validated.approvalStatus,
    notes: validated.reason,
  };
};

export const transformApprovalRequest = (data: ApprovalRequestType) => {
  const validated = approvalRequestSchema.parse(data);
  return {
    userId: validated.userId,
    approved: validated.approvalStatus === 'APPROVED',
    notes: validated.reason,
  };
};

export const transformComprehensiveUserManagementRequest = (data: ComprehensiveUserManagementRequestType) => {
  const validated = comprehensiveUserManagementRequestSchema.parse(data);

  const cleanData: Record<string, unknown> = {
    userId: validated.userId,
    operation: validated.operation,
    updates: validated.updates,
  };

  if (validated.reason !== undefined) cleanData.reason = validated.reason;

  return cleanData;
};

export const transformBulkOperationRequest = (data: BulkOperationRequestType) => {
  const validated = bulkOperationRequestSchema.parse(data);
  return {
    userIds: validated.userIds,
    operation: validated.operation,
    reason: validated.reason,
    operatorType: validated.operatorType,
  };
};
