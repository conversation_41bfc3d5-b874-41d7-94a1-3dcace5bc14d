import { globalPaginationBackendSchema } from '@/src/utils/pagination-transformer';
import { z } from 'zod';

import { imageBackedSchema } from '../../file';
import { currencyBackendSchema } from '../currencies/responses-transformers';
import { badgeApiResponseSchema, badgeBackendSchema } from '../badges';

export const paymentMethodTag = z.enum([
  'cryptocurrency',
  'ccpayment',
  'bank',
  'e-payment',
  'syriatel_credit',
  'syriatel_cash',
  'syriatel_invoice',
  'mtn_credit',
  'mtn_cash',
  'mtn_invoice',
  'syriatel_two_credit',
  'syriatel_two_cash',
  'syriatel_two_invoice',
  'mtn_two_credit',
  'mtn_two_cash',
  'mtn_two_invoice',
  'haram',
  'haram_glue',
  'haram_usd_cash',
  'haram_usd_cash_hama',
  'haram_usd_cash_homs',
  'haram_cash_damascus_zahera',
  'coinex',
  'payeer',
  'cwallet',
  'binance',
  'perfect_money',
  'bemo',
  'agent',
  'internal_agent',
]);

export const customFieldsTags = z
  .enum(['string', 'number', 'datetime', 'file', 'image', 'multiple', 'list']);
// custom fields schema in bach end
export const customFieldsSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().nullable().optional(),
    name_ar: z.string().nullable().optional(),
    type: customFieldsTags.or(z.string())
      .nullable()
      .optional(),
    placeholder: z.string().nullable().optional(),
    placeholder_ar: z.string().nullable().optional(),
    regex: z.string().nullable().optional(),
    slug: z.string().nullable().optional(),
    list_items: z.array(z.string().nullable().optional()).optional(),
  }).optional();

// custom fields schema in front end
export const customFieldsApiSchema = (i: z.infer<typeof customFieldsSchema>) => ({
  name: i?.name?.trim() ?? null,
  nameAr: i?.name_ar?.trim() ?? null,
  placeholder: i?.placeholder ?? null,
  placeholderAr: i?.placeholder_ar ?? null,
  regex: i?.regex ?? null,
  type: i?.type ?? null,
  listItems: i?.list_items || [],
  slug: i?.slug ?? null,
});
// payment method data schema return from back end
export const paymentMethodBackendSchema = z.object({
  id: z.number(),
  attributes: z.object({
    title: z.string(),
    title_ar: z.string().nullable().optional(),
    tag: paymentMethodTag,
    deposit_fees_fixed: z.string(),
    deposit_fees_percentage: z.string(),
    withdraw_fees_fixed: z.string(),
    withdraw_fees_percentage: z.string(),
    deposit_amount_min: z.string(),
    deposit_amount_max: z.string().nullable(),
    withdraw_amount_min: z.string(),
    withdraw_amount_max: z.string().nullable(),
    deposit_fees_min: z.string(),
    deposit_fees_max: z.string().nullable(),
    withdraw_fees_min: z.string(),
    withdraw_fees_max: z.string().nullable(),
    show_in_rates_bar: z.boolean().nullable().optional(),
    deposit_address: z.string().nullable(),
    short_description_withdraw: z.string().nullable(),
    short_description_withdraw_ar: z.string().nullable(),
    description_withdraw: z.string().nullable(),
    description_withdraw_ar: z.string().nullable(),
    short_description_deposit: z.string().nullable(),
    short_description_deposit_ar: z.string().nullable(),
    description_deposit: z.string().nullable(),
    description_deposit_ar: z.string().nullable(),
    deposit_currencies: z
      .object({
        data: z.array(
          z.object({
            id: z.number(),
            attributes: currencyBackendSchema,
          }),
        ),
      })
      .optional(),
    withdraw_currencies: z
      .object({
        data: z.array(
          z.object({
            id: z.number(),
            attributes: currencyBackendSchema,
          }),
        ),
      })
      .optional(),
    icon: z
      .object({
        data: z
          .object({
            id: z.number(),
            attributes: imageBackedSchema,
          })
          .nullable(),
      })
      .optional(),
    createdAt: z.string(),
    publishedAt: z.string().nullable(),
    updatedAt: z.string(),
    deposit_custom_fields: z.array(customFieldsSchema).optional(),
    withdraw_custom_fields: z.array(customFieldsSchema).optional(),
    badges: z.object({ data: z.array(badgeBackendSchema) }).optional().nullable(),
    created_by_user: z.object({ data: z.object({ id: z.number(), attributes: z.object({ email: z.string().nullable() }) }).nullable() }).optional(),
    updated_by_user: z
      .object({
        data: z
          .object({
            id: z.number(),
            attributes: z.object({
              email: z.string().nullable(),
            }),
          })
          .nullable(),
      })
      .optional(),
    order: z.number().min(1, { message: 'Order must be at least 1 ' }).nullable().optional(),
  }),
});
// transformer for currency schema to transform it from back end to front end schema.
export const currencyApiResponseSchema = (
  i: z.infer<typeof currencyBackendSchema> | undefined,
) => {
  if (i) {
    return {
      uid: i?.uid,
      label: i?.name,
      symbol: i?.symbol,
      code: i?.code,
      type: i?.type,
      active: i?.active,
      precision: i?.precision,
      allowExchangeFrom: i?.allow_exchange_from,
      allowExchangeTo: i?.allow_exchange_to,
      transferFeesPercent: i?.transfer_fees_percent,
      transferFeesFixed: i?.transfer_fees_fixed,
      exchangeFeesPercent: i?.exchange_fees_percent,
      exchangeFeesFixed: i?.exchange_fees_fixed,
      transferFeesMin: i?.transfer_fees_min,
      transferFeesMax: i?.transfer_fees_max,
      exchangeFeesMin: i?.exchange_fees_min,
      exchangeFeesMax: i?.exchange_fees_max,
      createdAt: i?.createdAt,
      showByDefault: i?.allow_exchange_from,
      image:
        i?.icon?.data?.attributes?.formats?.thumbnail.url
        ?? i?.icon?.data?.attributes.url,
    };
  }
  return null;
};
export const paymentMethodsBackendSchema = z.array(paymentMethodBackendSchema);
// payment method data transformer to transform back end schema to front end schema.
export const paymentMethodApiResponseSchema = (
  item: z.infer<typeof paymentMethodBackendSchema>,
) => ({
  id: item?.id,
  value: `${item?.id}`,
  label: item?.attributes?.title,
  labelAr: item?.attributes?.title_ar,
  image: {
    id: item?.attributes?.icon?.data?.id,
    url:
      item?.attributes?.icon?.data?.attributes?.formats?.thumbnail.url
      ?? item?.attributes?.icon?.data?.attributes.url,
  },
  tag: item?.attributes?.tag,
  depositFeesFixed: +item.attributes.deposit_fees_fixed,
  depositFeesPercentage: +item.attributes.deposit_fees_percentage,
  withdrawFeesFixed: +item.attributes.withdraw_fees_fixed,
  withdrawFeesPercentage: +item.attributes.withdraw_fees_percentage,
  depositAmountMin: +item.attributes.deposit_amount_min,
  depositAmountMax: item?.attributes?.deposit_amount_max
    ? +item.attributes.deposit_amount_max
    : null,
  withdrawAmountMin: +item.attributes.withdraw_amount_min,
  withdrawAmountMax: item?.attributes?.withdraw_amount_max
    ? +item.attributes.withdraw_amount_max
    : null,
  depositFeesMin: +item.attributes.deposit_fees_min,
  depositFeesMax: item.attributes.deposit_fees_max
    ? +item.attributes.deposit_fees_max
    : null,
  withdrawFeesMin: +item.attributes.withdraw_fees_min,
  withdrawFeesMax: item.attributes.withdraw_fees_max
    ? +item.attributes.withdraw_fees_max
    : null,
  showInRatesBar: item.attributes.show_in_rates_bar,
  depositAddress: item.attributes.deposit_address,
  depositCurrencies:
    item?.attributes?.deposit_currencies?.data?.map((i) => ({
      id: i?.id,
      paymentMethodTitle: item?.attributes?.title,
      paymentMethodIcon:
        item?.attributes?.icon?.data?.attributes?.formats?.thumbnail.url
        ?? item?.attributes?.icon?.data?.attributes.url,
      PaymentMethodFeesFixed: item?.attributes?.deposit_fees_fixed,
      PaymentMethodFeesPercentage: item?.attributes?.deposit_fees_percentage,
      ...currencyApiResponseSchema(i?.attributes),
    })) ?? [],
  withdrawCurrencies:
    item?.attributes?.withdraw_currencies?.data?.map((i) => ({
      id: i?.id,
      paymentMethodTitle: item?.attributes?.title,
      paymentMethodIcon:
        item?.attributes?.icon?.data?.attributes?.formats?.thumbnail.url
        ?? item?.attributes?.icon?.data?.attributes.url,
      PaymentMethodFeesFixed: item?.attributes?.deposit_fees_fixed,
      PaymentMethodFeesPercentage: item?.attributes?.deposit_fees_percentage,
      ...currencyApiResponseSchema(i?.attributes),
    })) ?? [],
  createdAt: item?.attributes?.createdAt,
  shortDescriptionDeposit: item?.attributes?.short_description_deposit,
  shortDescriptionDepositAr: item?.attributes?.short_description_deposit_ar,
  descriptionDeposit: item?.attributes?.description_deposit,
  descriptionDepositAr: item?.attributes?.description_deposit_ar,
  shortDescriptionWithdraw: item?.attributes?.short_description_withdraw,
  shortDescriptionWithdrawAr: item?.attributes?.short_description_withdraw_ar,
  descriptionWithdraw: item?.attributes?.description_withdraw,
  descriptionWithdrawAr: item?.attributes?.description_withdraw_ar,
  updatedAt: item?.attributes?.updatedAt,
  publishedAt: item?.attributes?.publishedAt,
  depositCustomFields: item?.attributes?.deposit_custom_fields?.map((i) => customFieldsApiSchema(i)),
  withdrawCustomFields: item?.attributes?.withdraw_custom_fields?.map((i) => customFieldsApiSchema(i)),
  badges: item.attributes?.badges?.data
    ? item.attributes?.badges?.data.map((badge) => badgeApiResponseSchema(badge))
    : null,
  createdBy: {
    id: item.attributes?.created_by_user?.data?.id,
    email: item.attributes?.created_by_user?.data?.attributes?.email,
  },
  updatedBy: {
    id: item.attributes?.updated_by_user?.data?.id,
    email: item.attributes?.updated_by_user?.data?.attributes?.email,
  },
  order: item?.attributes.order,
});
// payment methods response schema transformer
export const paymentMethodsApiResponseSchema = z
  .object({
    data: paymentMethodsBackendSchema,
    meta: globalPaginationBackendSchema,
  })
  .transform(({ data, meta }) => ({
    data: data.map(paymentMethodApiResponseSchema),
    pagination: {
      page: meta?.pagination?.page,
      pageSize: meta?.pagination?.pageSize,
      pageCount: meta?.pagination?.pageCount,
      total: meta?.pagination?.total,
    },
  }));
