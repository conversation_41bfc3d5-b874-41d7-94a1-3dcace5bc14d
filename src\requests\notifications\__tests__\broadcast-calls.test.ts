import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { QueryClient } from '@tanstack/react-query';
import { CLIENT_API } from '../../../lib/axios';
import { handleApiError } from '../../../utils/handle-backend-error';
import {
  sendBroadcastRequest,
  scheduleBroadcastRequest,
  previewBroadcastRequest,
  getRecipientOptionsRequest,
  validateRecipientsRequest,
  sendBroadcastMutation,
  scheduleBroadcastMutation,
  previewBroadcastMutation,
  getRecipientOptionsQuery,
  sendBroadcastMutationWithInvalidation,
} from '../broadcast-calls';
import {
  SendBroadcastRequest,
  ScheduleBroadcastRequest,
  PreviewBroadcastRequest,
  GetRecipientOptionsRequest,
} from '../../../types/notification-api.types';
import { NotificationPriority } from '../../../types/notification.types';

// Mock dependencies
jest.mock('../../../lib/axios');
jest.mock('../../../utils/handle-backend-error');
jest.mock('../broadcast-request-transformer', () => ({
  transformSendBroadcastRequest: jest.fn((data) => data),
  transformScheduleBroadcastRequest: jest.fn((data) => data),
  transformPreviewBroadcastRequest: jest.fn((data) => data),
  transformGetRecipientOptionsParams: jest.fn((params) => params),
}));
jest.mock('../broadcast-response-transformer', () => ({
  transformSendBroadcastResponse: jest.fn((data) => data),
  transformScheduleBroadcastResponse: jest.fn((data) => data),
  transformPreviewBroadcastResponse: jest.fn((data) => data),
  transformGetRecipientOptionsResponse: jest.fn((data) => data),
}));

const mockClientAPI = CLIENT_API as jest.Mocked<typeof CLIENT_API>;
const mockHandleApiError = handleApiError as jest.MockedFunction<typeof handleApiError>;

describe('Broadcast API Calls', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('sendBroadcastRequest', () => {
    const mockBroadcastData: SendBroadcastRequest = {
      title: 'Test Broadcast',
      content: 'This is a test broadcast message',
      priority: NotificationPriority.NORMAL,
      recipients: {
        type: 'all',
      },
    };

    const mockApiResponse = {
      data: {
        success: true,
        message: 'Broadcast sent successfully',
        data: {
          notificationId: 'broadcast-123',
          recipientCount: 150,
          estimatedDeliveryTime: 30,
        },
      },
    };

    it('should send broadcast message successfully', async () => {
      mockClientAPI.post.mockResolvedValue(mockApiResponse);

      const result = await sendBroadcastRequest(mockBroadcastData);

      expect(mockClientAPI.post).toHaveBeenCalledWith(
        '/notifications/broadcast/send',
        expect.any(Object)
      );
      expect(result).toBeDefined();
    });

    it('should handle API errors correctly', async () => {
      const mockError = {
        response: {
          data: {
            success: false,
            message: 'Invalid recipient selection',
          },
        },
      };

      mockClientAPI.post.mockRejectedValue(mockError);

      await expect(sendBroadcastRequest(mockBroadcastData)).rejects.toEqual(
        mockError.response.data
      );
      expect(mockHandleApiError).toHaveBeenCalledWith(mockError);
    });
  });

  describe('scheduleBroadcastRequest', () => {
    const mockScheduleData: ScheduleBroadcastRequest = {
      title: 'Scheduled Broadcast',
      content: 'This is a scheduled broadcast message',
      priority: NotificationPriority.HIGH,
      recipients: {
        type: 'role',
        roles: ['customer'],
      },
      scheduleTime: '2024-12-31T10:00:00Z',
    };

    const mockApiResponse = {
      data: {
        success: true,
        message: 'Broadcast scheduled successfully',
        data: {
          notificationId: 'scheduled-123',
          recipientCount: 75,
          scheduledAt: '2024-12-31T10:00:00Z',
          estimatedDeliveryTime: 60,
        },
      },
    };

    it('should schedule broadcast message successfully', async () => {
      mockClientAPI.post.mockResolvedValue(mockApiResponse);

      const result = await scheduleBroadcastRequest(mockScheduleData);

      expect(mockClientAPI.post).toHaveBeenCalledWith(
        '/notifications/broadcast/schedule',
        expect.any(Object)
      );
      expect(result).toBeDefined();
    });
  });

  describe('previewBroadcastRequest', () => {
    const mockPreviewData: PreviewBroadcastRequest = {
      title: 'Preview Broadcast',
      content: 'This is a preview of the broadcast message',
      recipients: {
        type: 'individual',
        userIds: ['user-1', 'user-2'],
      },
    };

    const mockApiResponse = {
      data: {
        success: true,
        message: 'Preview generated successfully',
        data: {
          renderedContent: 'This is a preview of the broadcast message',
          recipientCount: 2,
          estimatedDeliveryTime: 5,
          warnings: [],
        },
      },
    };

    it('should generate broadcast preview successfully', async () => {
      mockClientAPI.post.mockResolvedValue(mockApiResponse);

      const result = await previewBroadcastRequest(mockPreviewData);

      expect(mockClientAPI.post).toHaveBeenCalledWith(
        '/notifications/broadcast/preview',
        expect.any(Object)
      );
      expect(result).toBeDefined();
    });
  });

  describe('getRecipientOptionsRequest', () => {
    const mockParams: GetRecipientOptionsRequest = {
      search: 'john',
      type: 'user',
    };

    const mockApiResponse = {
      data: {
        success: true,
        message: 'Recipients retrieved successfully',
        data: {
          users: [
            {
              id: 'user-1',
              name: 'John Doe',
              email: '<EMAIL>',
              type: 'customer',
            },
          ],
          roles: [
            {
              id: 'role-1',
              name: 'Customer',
              userCount: 100,
            },
          ],
        },
      },
    };

    it('should get recipient options successfully', async () => {
      mockClientAPI.get.mockResolvedValue(mockApiResponse);

      const result = await getRecipientOptionsRequest(mockParams);

      expect(mockClientAPI.get).toHaveBeenCalledWith(
        '/notifications/broadcast/recipients?search=john&type=user'
      );
      expect(result).toBeDefined();
    });

    it('should handle empty parameters', async () => {
      mockClientAPI.get.mockResolvedValue(mockApiResponse);

      await getRecipientOptionsRequest({});

      expect(mockClientAPI.get).toHaveBeenCalledWith(
        '/notifications/broadcast/recipients'
      );
    });
  });

  describe('validateRecipientsRequest', () => {
    const mockRecipients = {
      type: 'role' as const,
      roles: ['customer', 'operator'],
    };

    const mockApiResponse = {
      data: {
        success: true,
        message: 'Recipients validated successfully',
        data: {
          isValid: true,
          recipientCount: 125,
          warnings: ['Some users may have disabled notifications'],
          errors: [],
        },
      },
    };

    it('should validate recipients successfully', async () => {
      mockClientAPI.post.mockResolvedValue(mockApiResponse);

      const result = await validateRecipientsRequest(mockRecipients);

      expect(mockClientAPI.post).toHaveBeenCalledWith(
        '/notifications/broadcast/validate-recipients',
        mockRecipients
      );
      expect(result).toBeDefined();
    });
  });

  describe('React Query Configurations', () => {
    it('should create getRecipientOptionsQuery with correct configuration', () => {
      const params = { search: 'test' };
      const query = getRecipientOptionsQuery(params);

      expect(query.queryKey).toEqual(['notification-broadcast', 'recipients', params]);
      expect(query.staleTime).toBe(300000); // 5 minutes
      expect(query.refetchOnWindowFocus).toBe(false);
    });

    it('should create sendBroadcastMutation with correct configuration', () => {
      const mutation = sendBroadcastMutation();

      expect(mutation.mutationKey).toEqual(['notification-broadcast', 'send']);
      expect(mutation.mutationFn).toBeDefined();
    });

    it('should create scheduleBroadcastMutation with correct configuration', () => {
      const mutation = scheduleBroadcastMutation();

      expect(mutation.mutationKey).toEqual(['notification-broadcast', 'schedule']);
      expect(mutation.mutationFn).toBeDefined();
    });

    it('should create previewBroadcastMutation with correct configuration', () => {
      const mutation = previewBroadcastMutation();

      expect(mutation.mutationKey).toEqual(['notification-broadcast', 'preview']);
      expect(mutation.mutationFn).toBeDefined();
    });
  });

  describe('Cache Invalidation', () => {
    it('should create sendBroadcastMutationWithInvalidation with cache invalidation', () => {
      const mockQueryClient = new QueryClient();
      const mockInvalidateQueries = jest.spyOn(mockQueryClient, 'invalidateQueries');

      const mutation = sendBroadcastMutationWithInvalidation(mockQueryClient);

      expect(mutation.mutationKey).toEqual(['notification-broadcast', 'send']);
      expect(mutation.onSuccess).toBeDefined();

      // Test onSuccess callback
      if (mutation.onSuccess) {
        mutation.onSuccess();
        // Note: In a real test, we would need to wait for the async operation
        // but for this unit test, we're just verifying the structure
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      mockClientAPI.post.mockRejectedValue(networkError);

      await expect(sendBroadcastRequest({
        title: 'Test',
        content: 'Test content',
        priority: NotificationPriority.NORMAL,
        recipients: { type: 'all' },
      })).rejects.toEqual(undefined);

      expect(mockHandleApiError).toHaveBeenCalledWith(networkError);
    });

    it('should handle validation errors', async () => {
      const validationError = {
        response: {
          data: {
            success: false,
            message: 'Validation failed',
            errors: ['Title is required', 'Content is required'],
          },
        },
      };

      mockClientAPI.post.mockRejectedValue(validationError);

      await expect(sendBroadcastRequest({
        title: '',
        content: '',
        priority: NotificationPriority.NORMAL,
        recipients: { type: 'all' },
      })).rejects.toEqual(validationError.response.data);
    });
  });
});