import { z } from 'zod';

// Zod schemas for response validation
export const userStatsSchema = z.object({
  total: z.number(),
  customers: z.number(),
  access_operators: z.number(),
  car_operators: z.number(),
  pending_approvals: z.number(),
  active_users: z.number().optional(),
  inactive_users: z.number().optional(),
});

export const shipmentStatsSchema = z.object({
  total: z.number(),
  active: z.number(),
  delivered: z.number(),
  cancelled: z.number().optional(),
  expired: z.number().optional(),
  delivery_rate: z.number(),
  pending: z.number().optional(),
  in_transit: z.number().optional(),
});

export const recentUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  user_type: z.string(),
  status: z.string(),
  created_at: z.string(),
});

export const recentShipmentSchema = z.object({
  id: z.string(),
  status: z.string(),
  created_at: z.string(),
}).optional();

export const quickActionSchema = z.object({
  label: z.string(),
  action: z.string(),
  icon: z.string(),
});

export const dashboardStatsSchema = z.object({
  user_stats: userStatsSchema,
  shipment_stats: shipmentStatsSchema,
  recent_users: z.array(recentUserSchema),
  recent_shipments: z.array(recentShipmentSchema),
  quick_actions: z.array(quickActionSchema),
});

export const dashboardResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    admin_info: z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
      role: z.string(),
      status: z.string(),
      email_verified: z.boolean(),
    }),
    dashboard_data: dashboardStatsSchema,
  }),
});

export const expiredShipmentStatsSchema = z.object({
  expired_shipments: z.object({
    total: z.number(),
    last_24h: z.number(),
    last_week: z.number(),
    last_month: z.number(),
  }),
  expiry_reasons: z.array(z.object({
    reason: z.string(),
    count: z.number(),
  })),
});

export const expiredShipmentStatsResponseSchema = z.object({
  success: z.boolean(),
  data: expiredShipmentStatsSchema,
});

// Type inference from schemas
export type UserStatsType = z.infer<typeof userStatsSchema>;
export type ShipmentStatsType = z.infer<typeof shipmentStatsSchema>;
export type RecentUserType = z.infer<typeof recentUserSchema>;
export type RecentShipmentType = z.infer<typeof recentShipmentSchema>;
export type QuickActionType = z.infer<typeof quickActionSchema>;
export type DashboardStatsType = z.infer<typeof dashboardStatsSchema>;
export type DashboardResponseType = z.infer<typeof dashboardResponseSchema>;
export type ExpiredShipmentStatsType = z.infer<typeof expiredShipmentStatsSchema>;
export type ExpiredShipmentStatsResponseType = z.infer<typeof expiredShipmentStatsResponseSchema>;
