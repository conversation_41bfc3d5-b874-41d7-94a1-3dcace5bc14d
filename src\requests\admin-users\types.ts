import { z } from 'zod';

// Zod schemas for request validation
export const userListParamsSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']).optional(),
  approval_status: z.enum(['APPROVED', 'PENDING', 'REJECTED']).optional(),
});

export const userStatusChangeRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']),
  reason: z.string().optional(),
});

export const userApprovalRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  approvalStatus: z.enum(['APPROVED', 'REJECTED']),
  reason: z.string().optional(),
});

export const approvalRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  approvalStatus: z.enum(['APPROVED', 'REJECTED']),
  reason: z.string().optional(),
  operatorType: z.enum(['ACCESS_OPERATOR', 'CAR_OPERATOR']),
});

export const comprehensiveUserManagementRequestSchema = z.object({
  operation: z.literal('management'),
  userId: z.string().min(1, 'User ID is required'),
  updates: z.object({
    status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']).optional(),
    approvalStatus: z.enum(['APPROVED', 'REJECTED', 'PENDING']).optional(),
    userType: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']).optional(),
    name: z.string().optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    country: z.string().optional(),
  }).refine((data) => Object.keys(data).length > 0, {
    message: 'At least one update field is required',
  }),
  reason: z.string().optional(),
});

export const bulkOperationRequestSchema = z.object({
  operation: z.enum(['approve', 'reject']),
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  reason: z.string().optional(),
  operatorType: z.enum(['ACCESS_OPERATOR', 'CAR_OPERATOR']),
});

// Response types
export interface UserListResponseType {
  success: boolean;
  data: {
    users: Array<{
      id: string;
      name: string;
      email: string;
      phone: string;
      user_type: string;
      status: string;
      approval_status: string;
      created_at: string;
      updated_at: string;
    }>;
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
  message?: string;
}

export interface UserDetailResponseType {
  success: boolean;
  data: {
    user: {
      id: string;
      name: string;
      email: string;
      phone: string;
      user_type: string;
      status: string;
      approval_status: string;
      created_at: string;
      updated_at: string;
      address?: string;
      city?: string;
      country?: string;
      profile_image?: string;
      documents?: Array<{
        id: string;
        type: string;
        url: string;
        status: string;
        created_at: string;
      }>;
      vehicles?: Array<{
        id: string;
        make: string;
        model: string;
        year: string;
        license_plate: string;
        status: string;
      }>;
      access_points?: Array<{
        id: string;
        name: string;
        address: string;
        city: string;
        country: string;
        status: string;
      }>;
    };
  };
  message?: string;
}

export interface UserStatusChangeResponseType {
  success: boolean;
  data: {
    user: {
      id: string;
      status: string;
      updated_at: string;
    };
  };
  message?: string;
}

export interface UserApprovalResponseType {
  success: boolean;
  data: {
    user: {
      id: string;
      approval_status: string;
      updated_at: string;
    };
  };
  message?: string;
}

export interface OperatorApprovalResponseType {
  success: boolean;
  data: {
    user: {
      id: string;
      user_type: string;
      approval_status: string;
      updated_at: string;
    };
  };
  message?: string;
}

export interface ComprehensiveUserManagementResponseType {
  success: boolean;
  data: {
    user: {
      id: string;
      name?: string;
      email?: string;
      phone?: string;
      user_type?: string;
      status?: string;
      approval_status?: string;
      updated_at: string;
    };
  };
  message?: string;
}

export interface BulkOperationResponseType {
  success: boolean;
  data: {
    processed: number;
    successful: number;
    failed: number;
    users: Array<{
      id: string;
      success: boolean;
      message?: string;
    }>;
  };
  message?: string;
}

// Type inference from schemas
export type UserListParamsType = z.infer<typeof userListParamsSchema>;
export type UserStatusChangeRequestType = z.infer<typeof userStatusChangeRequestSchema>;
export type UserApprovalRequestType = z.infer<typeof userApprovalRequestSchema>;

// New request types
export type ApprovalRequestType = z.infer<typeof approvalRequestSchema>;
export type ComprehensiveUserManagementRequestType = z.infer<typeof comprehensiveUserManagementRequestSchema>;
export type BulkOperationRequestType = z.infer<typeof bulkOperationRequestSchema>;
